openapi: 3.0.3
info:
  title: MCDC API
  version: v.0.0.1
  x-logo:
    url: https://example.com/logo.png
    altText: MCDC API
  description: API for MCDC
paths:
  /chat/check-status/:
    get:
      operationId: chat_check_status_retrieve
      tags:
      - Chat
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          description: No response body
  /chat/file/download/{message_id}/:
    get:
      operationId: chat_file_download_retrieve
      description: Download uploaded files from chat messages with authentication
        and permission checks
      summary: Download chat file
      parameters:
      - in: path
        name: message_id
        schema:
          type: integer
        required: true
      tags:
      - Chat
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          description: No response body
  /chat/file/metadata/{message_id}/:
    get:
      operationId: chat_file_metadata_retrieve
      description: Get metadata information about uploaded files in chat messages
      summary: Get file metadata
      parameters:
      - in: path
        name: message_id
        schema:
          type: integer
        required: true
      tags:
      - Chat
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          description: No response body
  /chat/files/:
    get:
      operationId: chat_files_retrieve
      description: Get list of files from chat messages with proper access control
        and filtering
      summary: List chat files
      tags:
      - Chat
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          description: No response body
  /chat/history/:
    get:
      operationId: chat_history_retrieve
      description: |-
        Get chat history for a specific conversation
        Supports both consultant and staff user types:
        - Consultants: Access their own chat room history automatically
        - Staff: Must provide user_consult_id parameter to specify consultant's room
      tags:
      - Chat
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          description: No response body
  /chat/mark-all-read/:
    post:
      operationId: chat_mark_all_read_create
      description: |-
        Mark all unread chat messages as read for the authenticated user

        For consultants: Marks all unread messages in their own room as read (consult_read = '1')
        For staff: Marks all unread messages as read for a specific consultant's room (users_read = '1')

        Request body (for staff only):
            - user_consult_id: ID of the consultant whose messages to mark as read (required for staff)

        Returns:
            APIResponse with count of messages marked as read
      tags:
      - Chat
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          description: No response body
  /chat/mark-read/:
    post:
      operationId: chat_mark_read_create
      description: |-
        Mark messages as read
        Only consultants and TcdUsers (staff) can mark messages as read
      tags:
      - Chat
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          description: No response body
  /chat/online-status/:
    post:
      operationId: chat_online_status_create
      description: |-
        Update user online status (simplified for existing models)
        Only consultants and TcdUsers (staff) can update status
      tags:
      - Chat
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          description: No response body
  /chat/prompts/:
    get:
      operationId: chat_prompts_retrieve
      description: |-
        Get available chat prompts/quick replies
        Only consultants and TcdUsers (staff) can access
      tags:
      - Chat
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          description: No response body
  /chat/prompts-answer/{prompt_id}/:
    post:
      operationId: chat_prompts_answer_create
      parameters:
      - in: path
        name: prompt_id
        schema:
          type: integer
        required: true
      tags:
      - Chat
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          description: No response body
  /chat/rooms/:
    get:
      operationId: chat_rooms_retrieve
      description: |-
        Get chat rooms for mobile application using existing TcdChat model
        Only consultants and TcdUsers (staff) can access
      tags:
      - Chat
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          description: No response body
  /chat/send/:
    post:
      operationId: chat_send_create
      description: |-
        Send a new chat message
        Only consultants and TcdUsers (staff) can send messages
      tags:
      - Chat
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          description: No response body
  /chat/settings/:
    get:
      operationId: chat_settings_retrieve
      description: |-
        Get chat settings
        Only consultants and TcdUsers (staff) can access
      tags:
      - Chat
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          description: No response body
  /chat/start/:
    post:
      operationId: chat_start_create
      description: |-
        Start a new chat session for mobile application using existing TcdChat model
        Only consultants can create/own chat rooms, TcdUsers (staff) can join
      tags:
      - Chat
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          description: No response body
  /consultant/:
    get:
      operationId: consultant_list
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - Consultant
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Consultant'
          description: ''
    post:
      operationId: consultant_create
      tags:
      - Consultant
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConsultantRequest'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Consultant'
          description: ''
  /consultant/{id}/:
    get:
      operationId: consultant_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this tcd user consult.
        required: true
      tags:
      - Consultant
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Consultant'
          description: ''
    put:
      operationId: consultant_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this tcd user consult.
        required: true
      tags:
      - Consultant
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConsultantRequest'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Consultant'
          description: ''
    patch:
      operationId: consultant_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this tcd user consult.
        required: true
      tags:
      - Consultant
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedConsultantRequest'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Consultant'
          description: ''
    delete:
      operationId: consultant_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this tcd user consult.
        required: true
      tags:
      - Consultant
      responses:
        '204':
          description: No response body
  /consultant/login/:
    post:
      operationId: consultant_login_create
      description: เข้าสู่ระบบสำหรับ Consultant
      summary: Consultant Login
      tags:
      - Consultant
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConsultantLoginRequest'
        required: true
      security:
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: true
                  error_message:
                    type: 'null'
                  error_code:
                    type: 'null'
                  data:
                    type: object
                    properties:
                      user:
                        type: object
                      tokens:
                        type: object
                        properties:
                          access:
                            type: string
                          refresh:
                            type: string
                      session_info:
                        type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
  /dashboard/category/:
    get:
      operationId: dashboard_category_list
      description: Get dashboard category
      tags:
      - Dashboard Category
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TcdDashboardCategory'
          description: ''
  /dashboard/list/:
    post:
      operationId: dashboard_list_create
      description: Get dashboard list with pagination
      tags:
      - Dashboard
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DashboardListParameterRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DashboardListParameterRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DashboardListParameterRequest'
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  error_code:
                    type: integer
                    example: null
                  error_message:
                    type: string
                    example: null
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/TcdDashboard'
                  page:
                    type: integer
                    example: 1
                  per_page:
                    type: integer
                    example: 10
                  total:
                    type: integer
                    example: 25
                  has_next:
                    type: boolean
                    example: true
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: ข้อมูลที่ส่งมาไม่ถูกต้อง
                  error_code:
                    type: integer
                    example: 2000
          description: ''
  /dashboard/overview/:
    post:
      operationId: dashboard_overview_create
      description: Returns comprehensive overview statistics including projects, members,
        consultants, and engagement metrics
      summary: Get dashboard overview statistics
      tags:
      - Dashboard
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DashboardParameterRequest'
            examples:
              DashboardOverviewRequest:
                value:
                  language: th
                summary: Get dashboard overview statistics
                description: Returns comprehensive overview statistics
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DashboardParameterRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DashboardParameterRequest'
      security:
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DashboardOverviewResponse'
              examples:
                DashboardOverviewRequest:
                  value:
                    language: th
                  summary: Get dashboard overview statistics
                  description: Returns comprehensive overview statistics
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: ข้อมูลที่ส่งมาไม่ถูกต้อง
                  error_code:
                    type: integer
                    example: 2000
          description: ''
        '500':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์
                  error_code:
                    type: integer
                    example: 5000
          description: ''
  /documents/list/:
    post:
      operationId: documents_list_create
      description: ดึงรายการข้อมูลเอกสารของที่ปรึกษา
      summary: Get document list
      tags:
      - Documents
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DocumentRequestRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/DocumentRequestRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/DocumentRequestRequest'
        required: true
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DocumentResponse'
          description: ''
  /faq/:
    get:
      operationId: faq_list
      description: Override list method to provide custom response format
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - FAQ
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedTcdAppFaqList'
          description: ''
  /faq/{id}/:
    get:
      operationId: faq_retrieve
      description: Override retrieve method to provide custom response format
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this tcd app faq.
        required: true
      tags:
      - FAQ
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TcdAppFaq'
          description: ''
  /faq/by_category/{category_id}/:
    get:
      operationId: faq_by_category_retrieve
      description: |-
        Get FAQs by category ID

        This endpoint provides FAQs filtered by a specific category.
        Supports all the same filtering and pagination options as the main list endpoint.
      parameters:
      - in: path
        name: category_id
        schema:
          type: integer
        description: FAQ Category ID
        required: true
      - in: query
        name: page
        schema:
          type: integer
        description: Page number
      - in: query
        name: page_size
        schema:
          type: integer
        description: 'Items per page (max: 100)'
      - in: query
        name: platform
        schema:
          type: string
          enum:
          - app
          - both
          - web
        description: Filter by platform
      tags:
      - FAQ
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TcdAppFaq'
          description: ''
  /faq/category/:
    get:
      operationId: faq_category_list
      description: Override list method to provide custom response format
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - FAQ Category
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedTcdAppFaqcategoryList'
          description: ''
  /faq/category/{id}/:
    get:
      operationId: faq_category_retrieve
      description: Override retrieve method to provide custom response format
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this tcd app faqcategory.
        required: true
      tags:
      - FAQ Category
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TcdAppFaqcategory'
          description: ''
  /login-by-code/:
    get:
      operationId: login_by_code_retrieve
      description: API สำหรับลงชื่อเข้าใช้งานด้วย ThaID
      tags:
      - Authentication
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /logout/:
    post:
      operationId: logout_create
      description: ออกจากระบบ - ทำให้ token หมดอายุ (รองรับทั้ง refresh และ access
        token)
      summary: Logout
      tags:
      - Token Management
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TokenRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/TokenRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/TokenRequest'
        required: true
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: true
                  error_message:
                    type: 'null'
                  error_code:
                    type: 'null'
                  data:
                    type: object
                    properties:
                      message:
                        type: string
                        example: ออกจากระบบสำเร็จ
                      token_type:
                        type: string
                        example: refresh
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: โทเค็นไม่ถูกต้อง
                  error_code:
                    type: integer
                    example: 1005
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
  /mas/department/:
    get:
      operationId: mas_department_list
      description: Department (อ่านอย่างเดียว)
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - Department
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedTcdAppMasDepartmentList'
          description: ''
  /mas/department/{id}/:
    get:
      operationId: mas_department_retrieve
      description: Department (อ่านอย่างเดียว)
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this tcd app mas department.
        required: true
      tags:
      - Department
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TcdAppMasDepartment'
          description: ''
  /mas/department/list-by-ministry/{ministry_id}/:
    get:
      operationId: mas_department_list_by_ministry_retrieve
      description: List departments by ministry
      parameters:
      - in: path
        name: ministry_id
        schema:
          type: string
          pattern: ^\d+$
        required: true
      tags:
      - Department
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TcdAppMasDepartment'
          description: ''
  /mas/government-sector/:
    get:
      operationId: mas_government_sector_list
      description: Government Sector (อ่านอย่างเดียว)
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - Government Sector
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedTcdAppMasGovernmentSectorList'
          description: ''
  /mas/government-sector/{id}/:
    get:
      operationId: mas_government_sector_retrieve
      description: Government Sector (อ่านอย่างเดียว)
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this tcd app mas government
          sector.
        required: true
      tags:
      - Government Sector
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TcdAppMasGovernmentSector'
          description: ''
  /mas/member-type/:
    get:
      operationId: mas_member_type_list
      description: Member Type (อ่านอย่างเดียว)
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - Member Type
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedTcdAppMasMemberTypeList'
          description: ''
  /mas/member-type/{id}/:
    get:
      operationId: mas_member_type_retrieve
      description: Member Type (อ่านอย่างเดียว)
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this tcd app mas member type.
        required: true
      tags:
      - Member Type
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TcdAppMasMemberType'
          description: ''
  /mas/ministry/:
    get:
      operationId: mas_ministry_list
      description: Ministry (อ่านอย่างเดียว)
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - Ministry
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedTcdAppMasMinistryList'
          description: ''
  /mas/ministry/{id}/:
    get:
      operationId: mas_ministry_retrieve
      description: Ministry (อ่านอย่างเดียว)
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this tcd app mas ministry.
        required: true
      tags:
      - Ministry
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TcdAppMasMinistry'
          description: ''
  /mas/ministry/list-by-government-sector/{government_sector_id}/:
    get:
      operationId: mas_ministry_list_by_government_sector_retrieve
      description: List ministries by government sector
      parameters:
      - in: path
        name: government_sector_id
        schema:
          type: string
          pattern: ^\d+$
        required: true
      tags:
      - Ministry
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TcdAppMasMinistry'
          description: ''
  /member/:
    get:
      operationId: member_list
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - Member
      responses:
        '200':
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Member'
          description: ''
    post:
      operationId: member_create
      tags:
      - Member
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/MemberRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/MemberRequest'
          application/json:
            schema:
              $ref: '#/components/schemas/MemberRequest'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Member'
          description: ''
  /member/{id}/:
    get:
      operationId: member_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this tcd app member.
        required: true
      tags:
      - Member
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Member'
          description: ''
    put:
      operationId: member_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this tcd app member.
        required: true
      tags:
      - Member
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/MemberRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/MemberRequest'
          application/json:
            schema:
              $ref: '#/components/schemas/MemberRequest'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Member'
          description: ''
    patch:
      operationId: member_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this tcd app member.
        required: true
      tags:
      - Member
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedMemberRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedMemberRequest'
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedMemberRequest'
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Member'
          description: ''
    delete:
      operationId: member_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this tcd app member.
        required: true
      tags:
      - Member
      responses:
        '204':
          description: No response body
  /member/change-password/:
    post:
      operationId: member_change_password_create
      description: เปลี่ยนรหัสผ่านสำหรับผู้ใช้ที่ล็อกอินแล้ว (ไม่ต้องผ่าน OTP)
      summary: Change Password
      tags:
      - Member
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ChangePasswordRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ChangePasswordRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ChangePasswordRequest'
        required: true
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: true
                  error_message:
                    type: 'null'
                  error_code:
                    type: 'null'
                  data:
                    type: object
                    properties:
                      message:
                        type: string
                        example: เปลี่ยนรหัสผ่านสำเร็จ
                      user_type:
                        type: string
                        example: member
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: รหัสผ่านปัจจุบันไม่ถูกต้อง
                  error_code:
                    type: integer
                    example: 1001
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '401':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: ไม่ได้รับอนุญาต
                  error_code:
                    type: integer
                    example: 1000
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
  /member/check-pid/:
    post:
      operationId: member_check_pid_create
      description: ตรวจสอบว่า PID (หมายเลขบัตรประชาชน) มีซ้ำในระบบหรือไม่
      summary: Check Identity Card Duplicate
      tags:
      - Member
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CheckPIDRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CheckPIDRequest'
          application/json:
            schema:
              $ref: '#/components/schemas/CheckPIDRequest'
        required: true
      security:
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: true
                  error_message:
                    type: 'null'
                  error_code:
                    type: 'null'
                  data:
                    type: object
                    properties:
                      is_duplicate:
                        type: boolean
                        example: false
                      message:
                        type: string
                        example: PID นี้สามารถใช้งานได้
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: PID นี้มีในระบบแล้ว
                  error_code:
                    type: integer
                    example: 2010
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
  /member/delete/:
    delete:
      operationId: member_delete_destroy
      description: ลบบัญชีสมาชิก Member โดยเช็คจาก token ว่าเป็นตัวเองเท่านั้นที่ลบได้
        พร้อมตรวจสอบรหัสผ่าน
      summary: Delete Member Account
      tags:
      - Member
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: true
                  error_message:
                    type: 'null'
                  error_code:
                    type: 'null'
                  data:
                    type: object
                    properties:
                      message:
                        type: string
                        example: ลบสมาชิกสำเร็จ
                      member_id:
                        type: integer
                        example: 123
                      username:
                        type: string
                        example: john_doe
                      deleted_at:
                        type: string
                        example: '2024-01-01T12:00:00Z'
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: รหัสผ่านปัจจุบันไม่ถูกต้อง
                  error_code:
                    type: integer
                    example: 1011
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '401':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: ไม่ได้รับอนุญาต
                  error_code:
                    type: integer
                    example: 1000
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '404':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: ไม่พบข้อมูลผู้ใช้
                  error_code:
                    type: integer
                    example: 1000
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
  /member/login/:
    post:
      operationId: member_login_create
      description: เข้าสู่ระบบสำหรับ Member
      summary: Member Login
      tags:
      - Member
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/MemberLoginRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/MemberLoginRequest'
          application/json:
            schema:
              $ref: '#/components/schemas/MemberLoginRequest'
        required: true
      security:
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: true
                  error_message:
                    type: 'null'
                  error_code:
                    type: 'null'
                  data:
                    type: object
                    properties:
                      user:
                        type: object
                      tokens:
                        type: object
                        properties:
                          access:
                            type: string
                          refresh:
                            type: string
                      session_info:
                        type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง
                  error_code:
                    type: integer
                    example: 1001
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
  /member/register/:
    post:
      operationId: member_register_create
      description: สมัครสมาชิก Member ใหม่ รองรับฟิลด์ทั้งหมดของ TcdAppMember และ
        OTP token validation
      summary: Member Registration
      tags:
      - Member
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/MemberRegistrationRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/MemberRegistrationRequest'
          application/json:
            schema:
              $ref: '#/components/schemas/MemberRegistrationRequest'
        required: true
      security:
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: true
                  error_message:
                    type: 'null'
                  error_code:
                    type: 'null'
                  data:
                    type: object
                    properties:
                      user:
                        type: object
                      tokens:
                        type: object
                        properties:
                          access:
                            type: string
                          refresh:
                            type: string
                      message:
                        type: string
                        example: สมัครสมาชิกสำเร็จ
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: ข้อมูลไม่ถูกต้อง
                  error_code:
                    type: integer
                    example: 2001
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
  /member/update-info/:
    put:
      operationId: member_update_info_update
      description: อัปเดตข้อมูลสมาชิก Member พร้อมการตรวจสอบรหัสผ่าน
      summary: Update Member Info
      tags:
      - Member
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateMemberInfoRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UpdateMemberInfoRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UpdateMemberInfoRequest'
        required: true
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: true
                  error_message:
                    type: 'null'
                  error_code:
                    type: 'null'
                  data:
                    type: object
                    properties:
                      message:
                        type: string
                        example: อัปเดตข้อมูลสมาชิกสำเร็จ
                      member:
                        type: object
                      updated_fields:
                        type: array
                        items:
                          type: string
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: รหัสผ่านปัจจุบันไม่ถูกต้อง
                  error_code:
                    type: integer
                    example: 1001
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '401':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: ไม่ได้รับอนุญาต
                  error_code:
                    type: integer
                    example: 1000
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
  /member/update-lang/:
    patch:
      operationId: member_update_lang_partial_update
      description: อัปเดตภาษาของสมาชิก Member โดยไม่ต้องตรวจสอบรหัสผ่าน
      summary: Update Member Language
      tags:
      - Member
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUpdateMemberLangRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUpdateMemberLangRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUpdateMemberLangRequest'
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: true
                  error_message:
                    type: 'null'
                  error_code:
                    type: 'null'
                  data:
                    type: object
                    properties:
                      message:
                        type: string
                        example: อัปเดตภาษาสำเร็จ
                      member:
                        type: object
                      updated_fields:
                        type: array
                        items:
                          type: string
                      old_lang:
                        type: string
                        example: th
                      new_lang:
                        type: string
                        example: en
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: ภาษาต้องเป็น 'th' หรือ 'en' เท่านั้น
                  error_code:
                    type: integer
                    example: 2001
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '401':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: ไม่ได้รับอนุญาต
                  error_code:
                    type: integer
                    example: 1000
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
  /member/upload-profile-picture/:
    post:
      operationId: member_upload_profile_picture_create
      description: อัปโหลดรูปโปรไฟล์สำหรับ Member ด้วย Base64 string - บันทึกลง member.src
      summary: Upload Profile Picture
      tags:
      - Member
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UploadProfilePictureRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UploadProfilePictureRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UploadProfilePictureRequest'
        required: true
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: true
                  error_message:
                    type: 'null'
                  error_code:
                    type: 'null'
                  data:
                    type: object
                    properties:
                      src:
                        type: string
                        example: data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...
                      base64_data:
                        type: string
                        example: /9j/4AAQSkZJRgABAQEAYABgAAD...
                      mime_type:
                        type: string
                        example: image/jpeg
                      file_size:
                        type: integer
                        example: 12345
                      message:
                        type: string
                        example: อัปโหลดรูปโปรไฟล์สำเร็จ
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: รูปแบบ base64 ไม่ถูกต้อง
                  error_code:
                    type: integer
                    example: 2001
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '401':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: ไม่ได้รับอนุญาต
                  error_code:
                    type: integer
                    example: 1000
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
  /member/validate-register/:
    post:
      operationId: member_validate_register_create
      description: ตรวจสอบว่าผู้ใช้งานสามารถสมัครสมาชิกได้หรือไม่
      summary: Check Register Member
      tags:
      - Member
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ValidateRegisterRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ValidateRegisterRequest'
          application/json:
            schema:
              $ref: '#/components/schemas/ValidateRegisterRequest'
        required: true
      security:
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: true
                  error_message:
                    type: 'null'
                  error_code:
                    type: 'null'
                  data:
                    type: object
                    properties:
                      message:
                        type: string
                        example: ok
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: ชื่อผู้ใช้งานนี้มีในระบบแล้ว
                  error_code:
                    type: integer
                    example: 2010
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
  /news/:
    get:
      operationId: news_list
      description: |-
        ViewSet for dashboard analytics and statistics

        Provides comprehensive dashboard functionality including:
        - Overview statistics
        - Project analytics
        - Consultant analytics
        - Member analytics
        - System health metrics
        - Search analytics
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - News
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedTcdNewsList'
          description: ''
  /news/{id}/:
    get:
      operationId: news_retrieve
      description: |-
        ViewSet for dashboard analytics and statistics

        Provides comprehensive dashboard functionality including:
        - Overview statistics
        - Project analytics
        - Consultant analytics
        - Member analytics
        - System health metrics
        - Search analytics
      parameters:
      - in: path
        name: id
        schema:
          type: integer
          maximum: **********
          minimum: -**********
        description: A unique value identifying this tcd news.
        required: true
      tags:
      - News
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TcdNews'
          description: ''
  /notification/:
    get:
      operationId: notification_list
      description: Get notifications with optional filtering
      summary: Get notifications
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - Notification
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedTcdAppNotificationList'
          description: ''
  /notification/{id}/:
    get:
      operationId: notification_list_2
      description: Get notifications with optional filtering
      summary: Get notifications
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this tcd app notification.
        required: true
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - Notification
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedTcdAppNotificationList'
          description: ''
  /notification/delete/:
    post:
      operationId: notification_delete_create
      description: Delete multiple notifications by providing a list of notification
        IDs
      summary: Delete notifications by IDs
      tags:
      - Notification
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                notification_ids:
                  type: array
                  items:
                    type: integer
                  description: List of notification IDs to delete
              required:
              - notification_ids
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  error_code:
                    type: string
                    nullable: true
                  error_message:
                    type: string
                    nullable: true
                  data:
                    type: object
                    properties:
                      deleted_count:
                        type: integer
                      deleted_ids:
                        type: array
                        items:
                          type: integer
                  api_version:
                    type: string
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  error_code:
                    type: string
                  error_message:
                    type: string
                  api_version:
                    type: string
          description: ''
  /notification/send/:
    post:
      operationId: notification_send_create
      description: Get notifications with optional filtering
      summary: Get notifications
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - Notification
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TcdAppNotificationRequest'
        required: true
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedTcdAppNotificationList'
          description: ''
  /otp/generate/:
    post:
      operationId: otp_generate_create
      description: Generate OTP for specified purpose (login, registration, etc.)
      summary: Generate OTP
      tags:
      - OTP
      requestBody:
        content:
          type:
            schema:
              type: object
              additionalProperties: {}
              description: Unspecified request body
          properties:
            schema:
              identifier:
                type: string
                description: User identifier (email)
              purpose:
                type: string
                description: Purpose of OTP (login, registration, reset_password,
                  etc.)
          required:
            schema:
              type: object
              additionalProperties: {}
              description: Unspecified request body
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: true
                  error_message:
                    type: 'null'
                  error_code:
                    type: 'null'
                  data:
                    type: object
                    properties:
                      token:
                        type: string
                      expires_at:
                        type: string
                        format: date-time
                      message:
                        type: string
                        example: OTP has been sent to your email
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: Invalid email format
                  error_code:
                    type: integer
                    example: 2002
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
  /otp/validate/:
    post:
      operationId: otp_validate_create
      description: Validate an already verified OTP token
      summary: Validate OTP Token
      tags:
      - OTP
      requestBody:
        content:
          type:
            schema:
              type: object
              additionalProperties: {}
              description: Unspecified request body
          properties:
            schema:
              token:
                type: string
                description: Verified JWT token
          required:
            schema:
              type: object
              additionalProperties: {}
              description: Unspecified request body
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: true
                  error_message:
                    type: 'null'
                  error_code:
                    type: 'null'
                  data:
                    type: object
                    properties:
                      identifier:
                        type: string
                      purpose:
                        type: string
                      verified_at:
                        type: string
                        format: date-time
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: Token has not been verified
                  error_code:
                    type: integer
                    example: 2026
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
  /otp/verify/:
    post:
      operationId: otp_verify_create
      description: Verify OTP code with the token
      summary: Verify OTP
      tags:
      - OTP
      requestBody:
        content:
          type:
            schema:
              type: object
              additionalProperties: {}
              description: Unspecified request body
          properties:
            schema:
              token:
                type: string
                description: JWT token from generate_otp
              otp:
                type: string
                description: OTP code
              ref_code:
                type: string
                description: Reference code
          required:
            schema:
              type: object
              additionalProperties: {}
              description: Unspecified request body
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: true
                  error_message:
                    type: 'null'
                  error_code:
                    type: 'null'
                  data:
                    type: object
                    properties:
                      token:
                        type: string
                      verified_at:
                        type: string
                        format: date-time
                      message:
                        type: string
                        example: OTP verified successfully
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: Invalid OTP
                  error_code:
                    type: integer
                    example: 2023
                  data:
                    type: object
                    properties:
                      token:
                        type: string
                      attempts:
                        type: integer
                        example: 1
                      remaining_attempts:
                        type: integer
                        example: 2
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
  /password-reset/request/:
    post:
      operationId: password_reset_request_create
      description: ขอรีเซ็ตรหัสผ่านโดยส่ง OTP ไปยังอีเมล
      summary: Request Password Reset
      tags:
      - Password Reset
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PasswordResetRequestRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PasswordResetRequestRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PasswordResetRequestRequest'
        required: true
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: true
                  error_message:
                    type: 'null'
                  error_code:
                    type: 'null'
                  data:
                    type: object
                    properties:
                      token:
                        type: string
                      ref_code:
                        type: string
                      expires_at:
                        type: string
                        format: date-time
                      message:
                        type: string
                        example: รหัส OTP สำหรับรีเซ็ตรหัสผ่านได้ถูกส่งไปยังอีเมลของคุณแล้ว
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: ไม่พบอีเมลนี้ในระบบ
                  error_code:
                    type: integer
                    example: 1000
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
  /password-reset/update-password/:
    post:
      operationId: password_reset_update_password_create
      description: อัปเดตรหัสผ่านใหม่หลังจากตรวจสอบ OTP สำเร็จ
      summary: Update Password Reset
      tags:
      - Password Reset
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PasswordResetUpdatePasswordRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PasswordResetUpdatePasswordRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PasswordResetUpdatePasswordRequest'
        required: true
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: true
                  error_message:
                    type: 'null'
                  error_code:
                    type: 'null'
                  data:
                    type: object
                    properties:
                      message:
                        type: string
                        example: รีเซ็ตรหัสผ่านสำเร็จ
                      user_type:
                        type: string
                        example: member
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: Token ไม่ถูกต้องหรือหมดอายุ
                  error_code:
                    type: integer
                    example: 2025
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
  /password-reset/verify-otp/:
    post:
      operationId: password_reset_verify_otp_create
      description: ตรวจสอบ OTP สำหรับการรีเซ็ตรหัสผ่าน
      summary: Verify Password Reset OTP
      tags:
      - Password Reset
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PasswordResetVerifyOTPRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PasswordResetVerifyOTPRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PasswordResetVerifyOTPRequest'
        required: true
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: true
                  error_message:
                    type: 'null'
                  error_code:
                    type: 'null'
                  data:
                    type: object
                    properties:
                      verified:
                        type: boolean
                        example: true
                      verified_token:
                        type: string
                      message:
                        type: string
                        example: ตรวจสอบ OTP สำเร็จ
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: รหัส OTP ไม่ถูกต้อง
                  error_code:
                    type: integer
                    example: 2023
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
  /payment/{payment_id}/:
    get:
      operationId: payment_retrieve
      description: ดึงรายละเอียดการชำระเงินตาม ID สำหรับที่ปรึกษาที่ล็อกอินอยู่
      summary: Get payment detail by ID
      parameters:
      - in: path
        name: payment_id
        schema:
          type: integer
        required: true
      tags:
      - Payment
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentDetailByIdResponse'
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
                description: Unspecified response body
          description: ''
        '401':
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
                description: Unspecified response body
          description: ''
        '403':
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
                description: Unspecified response body
          description: ''
        '404':
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
                description: Unspecified response body
          description: ''
        '500':
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
                description: Unspecified response body
          description: ''
  /payment/list/:
    get:
      operationId: payment_list_retrieve
      description: ดึงรายการรายละเอียดการชำระเงินของที่ปรึกษาที่ล็อกอินอยู่ - แสดง
        10 รายการล่าสุด เรียงลำดับจากวันที่เรียกเก็บมากไปน้อย
      summary: Get payment detail list
      parameters:
      - in: query
        name: page
        schema:
          type: integer
        description: 'หน้าที่ต้องการ (default: 1)'
      - in: query
        name: page_size
        schema:
          type: integer
        description: 'จำนวนรายการต่อหน้า (default: 10)'
      tags:
      - Payment
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentDetailListResponse'
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
                description: Unspecified response body
          description: ''
        '401':
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
                description: Unspecified response body
          description: ''
        '404':
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
                description: Unspecified response body
          description: ''
        '500':
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
                description: Unspecified response body
          description: ''
  /profile/:
    get:
      operationId: profile_retrieve
      description: Get user profile
      summary: Get user profile
      tags:
      - User
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          description: User profile
        '404':
          description: User not found
  /project/{app_project_id}/consultant-favorite/:
    get:
      operationId: project_consultant_favorite_retrieve
      description: หาสถานะการถูกใจเดิมของที่ปรึกษาต่อโครงการ (ดึง user_consult_id
        จาก JWT token)
      summary: Get Consultant Favorite Status
      parameters:
      - in: path
        name: app_project_id
        schema:
          type: string
        description: ID ของโครงการที่เลือก
        required: true
      tags:
      - Consultant Favorite
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                description: สำเร็จ
                content:
                  application/json:
                    example:
                      success: true
                      error_message: null
                      error_code: null
                      data:
                        id: 123
                        app_project_id: 456
                        user_consult_id: 789
                        consult_favorite: '1'
                        is_favorite: true
                      api_version: v.0.0.1
          description: ''
        '401':
          content:
            application/json:
              schema:
                description: ไม่ได้รับอนุญาต
                content:
                  application/json:
                    example:
                      success: false
                      error_message: Unauthorized
                      error_code: 4001
                      data: {}
                      api_version: v.0.0.1
          description: ''
        '404':
          content:
            application/json:
              schema:
                description: ไม่พบข้อมูล
                content:
                  application/json:
                    example:
                      success: false
                      error_message: Data not found
                      error_code: 3002
                      data: {}
                      api_version: v.0.0.1
          description: ''
  /project/{app_project_id}/consultant-favorite/update/:
    post:
      operationId: project_consultant_favorite_update_create
      description: 'อัปเดตสถานะการถูกใจของที่ปรึกษาต่อโครงการ (Toggle: ถ้าเดิม = ''0''
        เปลี่ยนเป็น ''1'', ถ้าไม่ใช่ เปลี่ยนเป็น ''0'') (ดึง user_consult_id จาก JWT
        token)'
      summary: Update Consultant Favorite Status
      parameters:
      - in: path
        name: app_project_id
        schema:
          type: string
        description: ID ของโครงการที่เลือก
        required: true
      tags:
      - Consultant Favorite
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConsultantFavoriteUpdateResponse'
          description: ''
        '401':
          content:
            application/json:
              schema:
                description: ไม่ได้รับอนุญาต
                content:
                  application/json:
                    example:
                      success: false
                      error_message: Unauthorized
                      error_code: 4001
                      data: {}
                      api_version: v.0.0.1
          description: ''
        '404':
          content:
            application/json:
              schema:
                description: ไม่พบข้อมูล
                content:
                  application/json:
                    example:
                      success: false
                      error_message: Data not found
                      error_code: 3002
                      data: {}
                      api_version: v.0.0.1
          description: ''
  /project/{app_project_id}/favorite/{user_consult_id}/:
    get:
      operationId: project_favorite_retrieve
      description: หาสถานะการถูกใจเดิมของสมาชิกต่อที่ปรึกษาในโครงการ
      summary: Get Member Favorite Status
      parameters:
      - in: path
        name: app_project_id
        schema:
          type: string
        description: ID ของโครงการที่เลือก
        required: true
      - in: path
        name: user_consult_id
        schema:
          type: string
        description: ID ของที่ปรึกษาที่เลือก
        required: true
      tags:
      - Project Member Favorite
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                description: สำเร็จ
                content:
                  application/json:
                    example:
                      success: true
                      error_message: null
                      error_code: null
                      data:
                        id: 123
                        app_project_id: 456
                        user_consult_id: 789
                        app_member_id: 101
                        member_favorite: '1'
                        is_favorite: true
                      api_version: v.0.0.1
          description: ''
        '404':
          content:
            application/json:
              schema:
                description: ไม่พบข้อมูล
                content:
                  application/json:
                    example:
                      success: false
                      error_message: Data not found
                      error_code: 3002
                      data: {}
                      api_version: v.0.0.1
          description: ''
  /project/{app_project_id}/matching/{user_consult_id}/view/increment/:
    post:
      operationId: project_matching_view_increment_create
      description: อัปเดตจำนวนการเข้าชมโครงการที่ matching (เพิ่ม 1)
      summary: Increment project matching view count
      parameters:
      - in: path
        name: app_project_id
        schema:
          type: integer
        description: ID ของโครงการ
        required: true
      - in: path
        name: user_consult_id
        schema:
          type: integer
        description: ID ของที่ปรึกษาที่ log in
        required: true
      tags:
      - Project Matching
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                description: Success
                content:
                  application/json:
                    example:
                      success: true
                      data:
                        id: 1
                        app_project_id: 123
                        user_consult_id: 456
                        old_consult_view: 5
                        new_consult_view: 6
                        matching: 85.5
                        updated: true
                      message: Success
          description: ''
        '404':
          content:
            application/json:
              schema:
                description: Project matching not found
                content:
                  application/json:
                    example:
                      success: false
                      error_code: 3002
                      error_message: Data not found
          description: ''
  /project/{project_id}/:
    get:
      operationId: project_retrieve
      description: ดึงข้อมูลรายละเอียดโครงการ พร้อมข้อมูลหน่วยงาน สาขา ความเชี่ยวชาญ
        การบริการ และข้อมูลการ matching สำหรับที่ปรึกษา
      summary: Get Project Detail
      parameters:
      - in: path
        name: project_id
        schema:
          type: integer
        description: ID ของโครงการที่ต้องการดูรายละเอียด
        required: true
      tags:
      - Project Detail
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectDetailResponse'
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
                description: Unspecified response body
          description: ''
        '404':
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
                description: Unspecified response body
          description: ''
        '500':
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
                description: Unspecified response body
          description: ''
  /project/{project_id}/consultants/:
    post:
      operationId: project_consultants_create
      description: ดึงรายการที่ปรึกษาที่เหมาะสมสำหรับโครงการ พร้อมการกรองและเรียงลำดับ
      summary: Get suitable consultants for a project
      parameters:
      - in: query
        name: page
        schema:
          type: integer
        description: 'Page number for pagination (default: 1)'
      - in: query
        name: page_size
        schema:
          type: integer
        description: 'Number of items per page (default: 10, max: 100)'
      - in: path
        name: project_id
        schema:
          type: string
        required: true
      tags:
      - Project Consultant
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SuitableConsultantRequestRequest'
            examples:
              BasicConsultantList:
                value: {}
                summary: ดึงรายการที่ปรึกษาพื้นฐาน
                description: ดึงรายการที่ปรึกษาที่เหมาะสมโดยไม่มีการกรอง
              IndependentConsultantsOnly:
                value:
                  consultant_type_filter_1: 1
                summary: กรองเฉพาะที่ปรึกษาอิสระ
                description: กรองที่ปรึกษาประเภทอิสระ (Type 1)
              CorporateConsultantsOnly:
                value:
                  consultant_type_filter_1: 2
                summary: กรองเฉพาะที่ปรึกษานิติบุคคล
                description: กรองที่ปรึกษาประเภทนิติบุคคล (Type 2)
              SpecificCorporateType:
                value:
                  consultant_type_filter_1: 2
                  consultant_type_filter_2: '1'
                summary: กรองตามประเภทกิจการเฉพาะ
                description: กรองที่ปรึกษานิติบุคคลตามประเภทกิจการ (เช่น ห้างหุ่นส่วน,
                  สถาบันการศึกษา)
              SortByMatchingScore:
                value:
                  sort_by_matching: true
                summary: เรียงตามผลการจับคู่ (มาก-น้อย)
                description: เรียงรายการที่ปรึกษาตามผลการจับคู่จากมากไปน้อย
              SortByLatestConsultants:
                value:
                  sort_by_latest: true
                summary: เรียงตามที่ปรึกษาล่าสุด
                description: เรียงรายการที่ปรึกษาตามความใหม่ของการลงทะเบียน
              CombinedFilters:
                value:
                  consultant_type_filter_1: 2
                  consultant_type_filter_2: '3'
                  sort_by_matching: true
                summary: การกรองแบบรวม
                description: กรองที่ปรึกษานิติบุคคลประเภทเฉพาะและเรียงตามผลการจับคู่
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/SuitableConsultantRequestRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/SuitableConsultantRequestRequest'
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuitableConsultantResponse'
              examples:
                BasicConsultantList:
                  value: {}
                  summary: ดึงรายการที่ปรึกษาพื้นฐาน
                  description: ดึงรายการที่ปรึกษาที่เหมาะสมโดยไม่มีการกรอง
                IndependentConsultantsOnly:
                  value:
                    consultant_type_filter_1: 1
                  summary: กรองเฉพาะที่ปรึกษาอิสระ
                  description: กรองที่ปรึกษาประเภทอิสระ (Type 1)
                CorporateConsultantsOnly:
                  value:
                    consultant_type_filter_1: 2
                  summary: กรองเฉพาะที่ปรึกษานิติบุคคล
                  description: กรองที่ปรึกษาประเภทนิติบุคคล (Type 2)
                SpecificCorporateType:
                  value:
                    consultant_type_filter_1: 2
                    consultant_type_filter_2: '1'
                  summary: กรองตามประเภทกิจการเฉพาะ
                  description: กรองที่ปรึกษานิติบุคคลตามประเภทกิจการ (เช่น ห้างหุ่นส่วน,
                    สถาบันการศึกษา)
                SortByMatchingScore:
                  value:
                    sort_by_matching: true
                  summary: เรียงตามผลการจับคู่ (มาก-น้อย)
                  description: เรียงรายการที่ปรึกษาตามผลการจับคู่จากมากไปน้อย
                SortByLatestConsultants:
                  value:
                    sort_by_latest: true
                  summary: เรียงตามที่ปรึกษาล่าสุด
                  description: เรียงรายการที่ปรึกษาตามความใหม่ของการลงทะเบียน
                CombinedFilters:
                  value:
                    consultant_type_filter_1: 2
                    consultant_type_filter_2: '3'
                    sort_by_matching: true
                  summary: การกรองแบบรวม
                  description: กรองที่ปรึกษานิติบุคคลประเภทเฉพาะและเรียงตามผลการจับคู่
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: ข้อมูลที่ส่งมาไม่ถูกต้อง
                  error_code:
                    type: integer
                    example: 2000
          description: ''
        '404':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: ไม่พบโครงการ
                  error_code:
                    type: integer
                    example: 3002
          description: ''
        '500':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์
                  error_code:
                    type: integer
                    example: 5000
          description: ''
  /project/{project_id}/favorites/:
    post:
      operationId: project_favorites_create
      description: ดึงรายการที่ปรึกษาที่ชอบในโครงการ (member_favorite = '1') พร้อม
        pagination และตัวเลือกการเรียงลำดับ
      summary: Get favorite consultants for a project
      parameters:
      - in: query
        name: page
        schema:
          type: integer
        description: 'Page number for pagination (default: 1)'
      - in: query
        name: page_size
        schema:
          type: integer
        description: 'Number of items per page (default: 10, max: 100)'
      - in: path
        name: project_id
        schema:
          type: string
        required: true
      tags:
      - Project Favorite Consultants
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProjectFavoriteConsultantsRequestRequest'
            examples:
              BasicRequest:
                value: {}
                summary: ดึงรายการที่ปรึกษาที่ชอบพื้นฐาน
                description: ดึงรายการที่ปรึกษาที่ชอบในโครงการโดยไม่มีการกรอง
              SortByMatchingRequest:
                value:
                  sort_by_matching: true
                summary: ดึงรายการที่ปรึกษาที่ชอบเรียงตามผลการจับคู่
                description: ดึงรายการที่ปรึกษาที่ชอบในโครงการเรียงลำดับตามผลการจับคู่จากมากไปน้อย
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ProjectFavoriteConsultantsRequestRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ProjectFavoriteConsultantsRequestRequest'
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectFavoriteConsultantsResponse'
              examples:
                BasicRequest:
                  value: {}
                  summary: ดึงรายการที่ปรึกษาที่ชอบพื้นฐาน
                  description: ดึงรายการที่ปรึกษาที่ชอบในโครงการโดยไม่มีการกรอง
                SortByMatchingRequest:
                  value:
                    sort_by_matching: true
                  summary: ดึงรายการที่ปรึกษาที่ชอบเรียงตามผลการจับคู่
                  description: ดึงรายการที่ปรึกษาที่ชอบในโครงการเรียงลำดับตามผลการจับคู่จากมากไปน้อย
          description: ''
        '404':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: ไม่พบโครงการ
                  error_code:
                    type: integer
                    example: 3002
          description: ''
        '500':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์
                  error_code:
                    type: integer
                    example: 5000
          description: ''
  /project/{project_id}/interested/:
    post:
      operationId: project_interested_create
      description: ดึงรายการที่ปรึกษาที่สนใจโครงการ (consult_send = 1) พร้อม pagination
        และตัวเลือกการเรียงลำดับ
      summary: Get interested consultants for a project
      parameters:
      - in: query
        name: page
        schema:
          type: integer
        description: 'Page number for pagination (default: 1)'
      - in: query
        name: page_size
        schema:
          type: integer
        description: 'Number of items per page (default: 10, max: 100)'
      - in: path
        name: project_id
        schema:
          type: string
        required: true
      tags:
      - Project Interested Consultants
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProjectInterestedConsultantsRequestRequest'
            examples:
              BasicRequest:
                value: {}
                summary: ดึงรายการผู้สนใจโครงการพื้นฐาน
                description: ดึงรายการที่ปรึกษาที่สนใจโครงการโดยไม่มีการกรอง
              SortByMatchingRequest:
                value:
                  sort_by_matching: true
                summary: ดึงรายการผู้สนใจโครงการเรียงตามผลการจับคู่
                description: ดึงรายการที่ปรึกษาที่สนใจโครงการเรียงลำดับตามผลการจับคู่จากมากไปน้อย
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ProjectInterestedConsultantsRequestRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ProjectInterestedConsultantsRequestRequest'
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectInterestedConsultantsResponse'
              examples:
                BasicRequest:
                  value: {}
                  summary: ดึงรายการผู้สนใจโครงการพื้นฐาน
                  description: ดึงรายการที่ปรึกษาที่สนใจโครงการโดยไม่มีการกรอง
                SortByMatchingRequest:
                  value:
                    sort_by_matching: true
                  summary: ดึงรายการผู้สนใจโครงการเรียงตามผลการจับคู่
                  description: ดึงรายการที่ปรึกษาที่สนใจโครงการเรียงลำดับตามผลการจับคู่จากมากไปน้อย
          description: ''
        '404':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: ไม่พบโครงการ
                  error_code:
                    type: integer
                    example: 3002
          description: ''
        '500':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์
                  error_code:
                    type: integer
                    example: 5000
          description: ''
  /project/{project_id}/view/increment/:
    post:
      operationId: project_view_increment_create
      description: "\n    API สำหรับเพิ่มจำนวนการเข้าชมโครงการ (เพิ่ม 1)\n\n    **Features:**\n\
        \    - เพิ่มจำนวนการเข้าชมโครงการ 1 ครั้ง\n    - ตรวจสอบการมีอยู่ของโครงการ\n\
        \    - ส่งคืนจำนวนการเข้าชมเดิมและใหม่\n    - Error handling ที่ครอบคลุม\n\
        \    "
      summary: เพิ่มจำนวนการเข้าชมโครงการ
      parameters:
      - in: path
        name: project_id
        schema:
          type: integer
        description: ID ของโครงการ
        required: true
      tags:
      - Project
      security:
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectViewIncrementResponse'
          description: ''
        '404':
          content:
            application/json:
              schema:
                description: ไม่พบโครงการ
                content:
                  application/json:
                    example:
                      success: false
                      error_code: 3002
                      message: Data not found
          description: ''
        '500':
          content:
            application/json:
              schema:
                description: ข้อผิดพลาดของระบบ
                content:
                  application/json:
                    example:
                      success: false
                      error_code: 5000
                      message: System error
          description: ''
  /project/consultant/experience/:
    post:
      operationId: project_consultant_experience_create
      description: ดึงรายการประสบการณ์ทั้งหมดของที่ปรึกษา พร้อมข้อมูลสาขา ความเชี่ยวชาญ
        และบริการของแต่ละโครงการ
      summary: Get consultant experience projects
      parameters:
      - in: query
        name: page
        schema:
          type: integer
        description: 'Page number for pagination (default: 1)'
      - in: query
        name: page_size
        schema:
          type: integer
        description: 'Number of items per page (default: 10, max: 100)'
      tags:
      - Consultant Experience
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConsultantExperienceRequestRequest'
            examples:
              IndependentConsultantRequest:
                value:
                  user_consult_id: 123
                  consult_type: 1
                  corporate_type_id: 456
                summary: ดึงรายการประสบการณ์ที่ปรึกษาอิสระ
                description: ดึงรายการประสบการณ์ทั้งหมดของที่ปรึกษาอิสระ
              CorporateConsultantRequest:
                value:
                  user_consult_id: 789
                  consult_type: 2
                  corporate_type_id: 101
                summary: ดึงรายการประสบการณ์ที่ปรึกษานิติบุคคล
                description: ดึงรายการประสบการณ์ทั้งหมดของที่ปรึกษานิติบุคคล
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ConsultantExperienceRequestRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ConsultantExperienceRequestRequest'
        required: true
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConsultantExperienceResponse'
              examples:
                IndependentConsultantRequest:
                  value:
                    user_consult_id: 123
                    consult_type: 1
                    corporate_type_id: 456
                  summary: ดึงรายการประสบการณ์ที่ปรึกษาอิสระ
                  description: ดึงรายการประสบการณ์ทั้งหมดของที่ปรึกษาอิสระ
                CorporateConsultantRequest:
                  value:
                    user_consult_id: 789
                    consult_type: 2
                    corporate_type_id: 101
                  summary: ดึงรายการประสบการณ์ที่ปรึกษานิติบุคคล
                  description: ดึงรายการประสบการณ์ทั้งหมดของที่ปรึกษานิติบุคคล
          description: ''
        '404':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: ไม่พบที่ปรึกษา
                  error_code:
                    type: integer
                    example: 3002
          description: ''
        '500':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์
                  error_code:
                    type: integer
                    example: 5000
          description: ''
  /project/consultant/favorite/:
    post:
      operationId: project_consultant_favorite_create
      description: ดึงรายการโครงการที่ชอบสำหรับที่ปรึกษา (consult_favorite = '1')
        พร้อม pagination และตัวเลือกการเรียงลำดับ
      summary: Get favorite projects for consultant
      parameters:
      - in: query
        name: page
        schema:
          type: integer
        description: 'Page number for pagination (default: 1)'
      - in: query
        name: page_size
        schema:
          type: integer
        description: 'Number of items per page (default: 10, max: 100)'
      tags:
      - Consultant Favorite Projects
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConsultantFavoriteProjectsRequestRequest'
            examples:
              BasicRequest:
                value: {}
                summary: ดึงรายการโครงการที่ชอบพื้นฐาน
                description: ดึงรายการโครงการที่ชอบของที่ปรึกษาโดยไม่มีการกรอง
              SortByMatchingRequest:
                value:
                  sort_by_matching: true
                summary: ดึงรายการโครงการที่ชอบเรียงตามผลการจับคู่
                description: ดึงรายการโครงการที่ชอบของที่ปรึกษาเรียงลำดับตามผลการจับคู่จากมากไปน้อย
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ConsultantFavoriteProjectsRequestRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ConsultantFavoriteProjectsRequestRequest'
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConsultantFavoriteProjectsResponse'
              examples:
                BasicRequest:
                  value: {}
                  summary: ดึงรายการโครงการที่ชอบพื้นฐาน
                  description: ดึงรายการโครงการที่ชอบของที่ปรึกษาโดยไม่มีการกรอง
                SortByMatchingRequest:
                  value:
                    sort_by_matching: true
                  summary: ดึงรายการโครงการที่ชอบเรียงตามผลการจับคู่
                  description: ดึงรายการโครงการที่ชอบของที่ปรึกษาเรียงลำดับตามผลการจับคู่จากมากไปน้อย
          description: ''
        '401':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: Unauthorized
                  error_code:
                    type: integer
                    example: 4001
          description: ''
        '500':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์
                  error_code:
                    type: integer
                    example: 5000
          description: ''
  /project/consultant/member-view/increment/:
    post:
      operationId: project_consultant_member_view_increment_create
      description: อัปเดตจำนวนการเข้าชมที่ปรึกษาโดยสมาชิก (เพิ่ม 1)
      summary: Increment consultant member view count
      tags:
      - Consultant Member View
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConsultantMemberViewRequestRequest'
            examples:
              BasicRequest:
                value:
                  app_project_id: 123
                  user_consult_id: 456
                summary: อัปเดตจำนวนการเข้าชมที่ปรึกษา
                description: เพิ่มจำนวนการเข้าชมที่ปรึกษาโดยสมาชิก 1 ครั้ง
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ConsultantMemberViewRequestRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ConsultantMemberViewRequestRequest'
        required: true
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConsultantMemberViewResponse'
              examples:
                BasicRequest:
                  value:
                    app_project_id: 123
                    user_consult_id: 456
                  summary: อัปเดตจำนวนการเข้าชมที่ปรึกษา
                  description: เพิ่มจำนวนการเข้าชมที่ปรึกษาโดยสมาชิก 1 ครั้ง
          description: ''
        '400':
          content:
            application/json:
              schema:
                description: ข้อมูลที่ส่งมาไม่ถูกต้อง
                content:
                  application/json:
                    example:
                      success: false
                      error_code: 2000
                      error_message: ข้อมูลที่ส่งมาไม่ถูกต้อง
          description: ''
        '404':
          content:
            application/json:
              schema:
                description: ไม่พบข้อมูล
                content:
                  application/json:
                    example:
                      success: false
                      error_code: 3002
                      error_message: Data not found
          description: ''
        '500':
          content:
            application/json:
              schema:
                description: ข้อผิดพลาดของระบบ
                content:
                  application/json:
                    example:
                      success: false
                      error_code: 5000
                      error_message: System error
          description: ''
  /project/consultant/suitable/:
    post:
      operationId: project_consultant_suitable_create
      description: ดึงรายการโครงการที่เหมาะสมสำหรับที่ปรึกษา โดยใช้เงื่อนไข matching
        >= project.result และโครงการที่ยังเปิดรับสมัคร
      summary: Get suitable projects for consultant
      parameters:
      - in: query
        name: page
        schema:
          type: integer
        description: 'Page number for pagination (default: 1)'
      - in: query
        name: page_size
        schema:
          type: integer
        description: 'Number of items per page (default: 10, max: 100)'
      tags:
      - Consultant Suitable Projects
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConsultantSuitableProjectsRequestRequest'
            examples:
              BasicRequest:
                value: {}
                summary: ดึงรายการโครงการที่เหมาะสมพื้นฐาน
                description: ดึงรายการโครงการที่เหมาะสมโดยไม่มีการกรอง
              SectorFilterRequest:
                value:
                  sector_id: 1
                summary: กรองตามสาขาเฉพาะ
                description: ดึงรายการโครงการที่เหมาะสมในสาขาที่กำหนด
              SortByMatchingRequest:
                value:
                  sort_by_matching: true
                summary: เรียงตามผลการจับคู่
                description: เรียงรายการโครงการตามผลการจับคู่จากมากไปน้อย
              CombinedFilterRequest:
                value:
                  sector_id: 2
                  sort_by_matching: true
                summary: กรองและเรียงแบบรวม
                description: กรองตามสาขาและเรียงตามผลการจับคู่
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ConsultantSuitableProjectsRequestRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ConsultantSuitableProjectsRequestRequest'
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConsultantSuitableProjectsResponse'
              examples:
                BasicRequest:
                  value: {}
                  summary: ดึงรายการโครงการที่เหมาะสมพื้นฐาน
                  description: ดึงรายการโครงการที่เหมาะสมโดยไม่มีการกรอง
                SectorFilterRequest:
                  value:
                    sector_id: 1
                  summary: กรองตามสาขาเฉพาะ
                  description: ดึงรายการโครงการที่เหมาะสมในสาขาที่กำหนด
                SortByMatchingRequest:
                  value:
                    sort_by_matching: true
                  summary: เรียงตามผลการจับคู่
                  description: เรียงรายการโครงการตามผลการจับคู่จากมากไปน้อย
                CombinedFilterRequest:
                  value:
                    sector_id: 2
                    sort_by_matching: true
                  summary: กรองและเรียงแบบรวม
                  description: กรองตามสาขาและเรียงตามผลการจับคู่
          description: ''
        '401':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: ไม่ได้รับอนุญาต
                  error_code:
                    type: integer
                    example: 4001
          description: ''
        '500':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์
                  error_code:
                    type: integer
                    example: 5000
          description: ''
  /project/corporate/type/:
    get:
      operationId: project_corporate_type_retrieve
      description: ดึงข้อมูลประเภทกิจการ
      summary: Get corporate type
      tags:
      - Corporate Type
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TcdCorporateType'
          description: ''
  /project/count/:
    post:
      operationId: project_count_create
      description: Returns the total count of active projects with comprehensive filtering
        capabilities
      summary: Get project count with advanced filtering
      tags:
      - Project
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProjectCountRequestRequest'
            examples:
              BasicCount:
                value: {}
                summary: Get total project count without filters
                description: Returns total count of all active projects
              FilteredCount:
                value:
                  project_name: development
                  organization_name: ministry
                  sector_ids: 1,2,3
                  project_period_start: '2024-01-01'
                  project_period_end: '2024-12-31'
                summary: Get project count with filters
                description: Returns count of projects matching the specified criteria
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ProjectCountRequestRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ProjectCountRequestRequest'
      security:
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectCountResponse'
              examples:
                BasicCount:
                  value: {}
                  summary: Get total project count without filters
                  description: Returns total count of all active projects
                FilteredCount:
                  value:
                    project_name: development
                    organization_name: ministry
                    sector_ids: 1,2,3
                    project_period_start: '2024-01-01'
                    project_period_end: '2024-12-31'
                  summary: Get project count with filters
                  description: Returns count of projects matching the specified criteria
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: ข้อมูลที่ส่งมาไม่ถูกต้อง
                  error_code:
                    type: integer
                    example: 2000
          description: ''
        '500':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์
                  error_code:
                    type: integer
                    example: 5000
          description: ''
  /project/detail/extended/:
    post:
      operationId: project_detail_extended_create
      description: ดึงข้อมูลรายละเอียดโครงการแบบละเอียดพร้อมข้อมูล master data ทั้งหมด
        รวมถึงประสบการณ์ที่ปรึกษา ผลลัพธ์ ค่าใช้จ่าย ประเภทโครงการ และอื่นๆ
      summary: Get Extended Project Detail
      tags:
      - Project Detail Extended
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProjectDetailExtendedRequestRequest'
            examples:
              BasicRequest:
                value:
                  app_project_id: 123
                summary: ดึงข้อมูลโครงการแบบละเอียด
                description: ดึงข้อมูลโครงการพร้อม master data ทั้งหมด
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ProjectDetailExtendedRequestRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ProjectDetailExtendedRequestRequest'
        required: true
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectDetailExtendedResponse'
              examples:
                BasicRequest:
                  value:
                    app_project_id: 123
                  summary: ดึงข้อมูลโครงการแบบละเอียด
                  description: ดึงข้อมูลโครงการพร้อม master data ทั้งหมด
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: ข้อมูลที่ส่งมาไม่ถูกต้อง
                  error_code:
                    type: integer
                    example: 2000
          description: ''
        '404':
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: ไม่พบโครงการ
                  error_code:
                    type: integer
                    example: 3002
          description: ''
        '500':
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์
                  error_code:
                    type: integer
                    example: 5000
          description: ''
  /project/favorite/update/:
    post:
      operationId: project_favorite_update_create
      description: 'อัปเดตสถานะการถูกใจของสมาชิกต่อที่ปรึกษาในโครงการ (Toggle: ถ้าเดิม
        = ''0'' เปลี่ยนเป็น ''1'', ถ้าไม่ใช่ เปลี่ยนเป็น ''0'')'
      summary: Update Member Favorite Status
      tags:
      - Project Member Favorite
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProjectMemberFavoriteRequestRequest'
            examples:
              ToggleFavoriteStatus:
                value:
                  app_project_id: 123
                  user_consult_id: 456
                summary: อัปเดตสถานะการถูกใจ
                description: Toggle สถานะการถูกใจของสมาชิกต่อที่ปรึกษาในโครงการ
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ProjectMemberFavoriteRequestRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ProjectMemberFavoriteRequestRequest'
        required: true
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectMemberFavoriteUpdateResponse'
              examples:
                ToggleFavoriteStatus:
                  value:
                    app_project_id: 123
                    user_consult_id: 456
                  summary: อัปเดตสถานะการถูกใจ
                  description: Toggle สถานะการถูกใจของสมาชิกต่อที่ปรึกษาในโครงการ
          description: ''
        '400':
          content:
            application/json:
              schema:
                description: ข้อมูลที่ส่งมาไม่ถูกต้อง
                content:
                  application/json:
                    example:
                      success: false
                      error_message: ข้อมูลที่ส่งมาไม่ถูกต้อง
                      error_code: 2000
                      data: {}
                      api_version: v.0.0.1
          description: ''
        '404':
          content:
            application/json:
              schema:
                description: ไม่พบข้อมูล
                content:
                  application/json:
                    example:
                      success: false
                      error_message: Data not found
                      error_code: 3002
                      data: {}
                      api_version: v.0.0.1
          description: ''
  /project/interest/status/:
    post:
      operationId: project_interest_status_create
      description: หาสถานะการยื่นความสนใจเดิม (ดึง user_consult_id จาก JWT token)
      summary: Get Project Interest Status
      tags:
      - Project Interest
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProjectInterestStatusRequestRequest'
            examples:
              BasicRequest:
                value:
                  app_project_id: 456
                summary: ตรวจสอบสถานะความสนใจ
                description: ตรวจสอบสถานะการยื่นความสนใจของที่ปรึกษาต่อโครงการ (user_consult_id
                  จะถูกดึงจาก JWT token)
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ProjectInterestStatusRequestRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ProjectInterestStatusRequestRequest'
        required: true
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectInterestStatusResponse'
              examples:
                BasicRequest:
                  value:
                    app_project_id: 456
                  summary: ตรวจสอบสถานะความสนใจ
                  description: ตรวจสอบสถานะการยื่นความสนใจของที่ปรึกษาต่อโครงการ (user_consult_id
                    จะถูกดึงจาก JWT token)
          description: ''
        '400':
          content:
            application/json:
              schema:
                description: ข้อมูลที่ส่งมาไม่ถูกต้อง
                content:
                  application/json:
                    example:
                      status: false
                      error_message: ข้อมูลที่ส่งมาไม่ถูกต้อง
                      error_code: 2000
                      data: {}
                      api_version: v.0.0.1
          description: ''
        '401':
          content:
            application/json:
              schema:
                description: ไม่ได้รับอนุญาต
                content:
                  application/json:
                    example:
                      status: false
                      error_message: Unauthorized
                      error_code: 4001
                      data: {}
                      api_version: v.0.0.1
          description: ''
        '404':
          content:
            application/json:
              schema:
                description: ไม่พบข้อมูล
                content:
                  application/json:
                    example:
                      status: false
                      error_message: ไม่พบข้อมูล
                      error_code: 3002
                      data: {}
                      api_version: v.0.0.1
          description: ''
  /project/interest/update/:
    post:
      operationId: project_interest_update_create
      description: 'อัปเดตสถานะการยื่นความสนใจโครงการ (Toggle: ถ้าเดิม = 0 เปลี่ยนเป็น
        1, ถ้าไม่ใช่ เปลี่ยนเป็น 0) (ดึง user_consult_id จาก JWT token)'
      summary: Update Project Interest Status
      tags:
      - Project Interest
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProjectInterestUpdateRequestRequest'
            examples:
              ToggleInterestStatus:
                value:
                  app_project_id: 456
                summary: อัปเดตสถานะความสนใจ
                description: Toggle สถานะการยื่นความสนใจของที่ปรึกษาต่อโครงการ (user_consult_id
                  จะถูกดึงจาก JWT token)
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ProjectInterestUpdateRequestRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ProjectInterestUpdateRequestRequest'
        required: true
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProjectInterestUpdateResponse'
              examples:
                ToggleInterestStatus:
                  value:
                    app_project_id: 456
                  summary: อัปเดตสถานะความสนใจ
                  description: Toggle สถานะการยื่นความสนใจของที่ปรึกษาต่อโครงการ (user_consult_id
                    จะถูกดึงจาก JWT token)
          description: ''
        '400':
          content:
            application/json:
              schema:
                description: ข้อมูลที่ส่งมาไม่ถูกต้อง
                content:
                  application/json:
                    example:
                      status: false
                      error_message: ข้อมูลที่ส่งมาไม่ถูกต้อง
                      error_code: 2000
                      data: {}
                      api_version: v.0.0.1
          description: ''
        '401':
          content:
            application/json:
              schema:
                description: ไม่ได้รับอนุญาต
                content:
                  application/json:
                    example:
                      status: false
                      error_message: Unauthorized
                      error_code: 4001
                      data: {}
                      api_version: v.0.0.1
          description: ''
        '404':
          content:
            application/json:
              schema:
                description: ไม่พบข้อมูล
                content:
                  application/json:
                    example:
                      status: false
                      error_message: ไม่พบข้อมูล
                      error_code: 3002
                      data: {}
                      api_version: v.0.0.1
          description: ''
  /project/member/search/:
    post:
      operationId: project_member_search_create
      description: ค้นหาโครงการของสมาชิก พร้อมการกรองข้อมูลและการเรียงลำดับ
      summary: Search member's projects
      parameters:
      - in: query
        name: page
        schema:
          type: integer
        description: 'Page number for pagination (default: 1)'
      - in: query
        name: page_size
        schema:
          type: integer
        description: 'Number of items per page (default: 10, max: 100)'
      tags:
      - Member Project
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MemberProjectSearchRequestRequest'
            examples:
              LatestProjects:
                value:
                  project_name: โครงการพัฒนา
                  filter_type: latest
                summary: ค้นหาโครงการล่าสุด
                description: ค้นหาโครงการล่าสุดตามชื่อโครงการ
              PublishedProjects:
                value:
                  project_name: โครงการ
                  announcement_start_date: '2024-01-01'
                  announcement_end_date: '2024-12-31'
                  filter_type: published
                summary: ค้นหาโครงการที่ประกาศ
                description: ค้นหาโครงการที่มีสถานะเผยแพร่แล้ว
              AnnouncementCountFilter:
                value:
                  filter_type: announcement_count
                summary: กรองตามจำนวนที่ประกาศ
                description: เรียงโครงการตามจำนวนการเข้าชม (view count)
              DateRangeSearch:
                value:
                  announcement_start_date: '2024-01-01'
                  announcement_end_date: '2024-06-30'
                  filter_type: latest
                summary: ค้นหาตามช่วงวันที่ประกาศ
                description: ค้นหาโครงการในช่วงวันที่ประกาศที่กำหนด
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/MemberProjectSearchRequestRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/MemberProjectSearchRequestRequest'
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MemberProjectSearchResponse'
              examples:
                LatestProjects:
                  value:
                    project_name: โครงการพัฒนา
                    filter_type: latest
                  summary: ค้นหาโครงการล่าสุด
                  description: ค้นหาโครงการล่าสุดตามชื่อโครงการ
                PublishedProjects:
                  value:
                    project_name: โครงการ
                    announcement_start_date: '2024-01-01'
                    announcement_end_date: '2024-12-31'
                    filter_type: published
                  summary: ค้นหาโครงการที่ประกาศ
                  description: ค้นหาโครงการที่มีสถานะเผยแพร่แล้ว
                AnnouncementCountFilter:
                  value:
                    filter_type: announcement_count
                  summary: กรองตามจำนวนที่ประกาศ
                  description: เรียงโครงการตามจำนวนการเข้าชม (view count)
                DateRangeSearch:
                  value:
                    announcement_start_date: '2024-01-01'
                    announcement_end_date: '2024-06-30'
                    filter_type: latest
                  summary: ค้นหาตามช่วงวันที่ประกาศ
                  description: ค้นหาโครงการในช่วงวันที่ประกาศที่กำหนด
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: ข้อมูลที่ส่งมาไม่ถูกต้อง
                  error_code:
                    type: integer
                    example: 2000
          description: ''
        '401':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: ไม่ได้รับอนุญาต
                  error_code:
                    type: integer
                    example: 4001
          description: ''
        '500':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์
                  error_code:
                    type: integer
                    example: 5000
          description: ''
  /project/search/:
    post:
      operationId: project_search_create
      description: Advanced project search endpoint with comprehensive filtering,
        sorting, and detailed response data
      summary: Advanced project search with comprehensive filtering and sorting
      parameters:
      - in: query
        name: page
        schema:
          type: integer
        description: 'Page number for pagination (default: 1)'
      - in: query
        name: page_size
        schema:
          type: integer
        description: 'Number of items per page (default: 10, max: 100)'
      tags:
      - Project
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProjectSearchRequestRequest'
            examples:
              BasicSearch:
                value:
                  project_name: development
                summary: Basic project name search
                description: Search projects by name with default sorting
              AdvancedSearch:
                value:
                  project_name: infrastructure
                  organization_name: ministry
                  sector_ids: 1,2,3
                  project_period_start: '2024-01-01'
                  project_period_end: '2024-12-31'
                  min_view_count: 10
                  sort_by: -view_count
                summary: Advanced search with multiple filters
                description: Search with multiple criteria and custom sorting
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ProjectSearchRequestRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ProjectSearchRequestRequest'
      security:
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: array
                    items:
                      type: object
                      properties:
                        project_id:
                          type: integer
                        project_name:
                          type: string
                        organization_name:
                          type: string
                        announcement_period:
                          type: string
                        sectors:
                          type: string
                        view_count:
                          type: integer
                        matching_result:
                          type: number
                          nullable: true
              examples:
                BasicSearch:
                  value:
                    project_name: development
                  summary: Basic project name search
                  description: Search projects by name with default sorting
                AdvancedSearch:
                  value:
                    project_name: infrastructure
                    organization_name: ministry
                    sector_ids: 1,2,3
                    project_period_start: '2024-01-01'
                    project_period_end: '2024-12-31'
                    min_view_count: 10
                    sort_by: -view_count
                  summary: Advanced search with multiple filters
                  description: Search with multiple criteria and custom sorting
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: ข้อมูลที่ส่งมาไม่ถูกต้อง
                  error_code:
                    type: integer
                    example: 2000
          description: ''
        '500':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: เกิดข้อผิดพลาดภายในเซิร์ฟเวอร์
                  error_code:
                    type: integer
                    example: 5000
          description: ''
  /refresh/:
    post:
      operationId: refresh_create
      description: ต่ออายุ Access Token ด้วย Refresh Token และ revoke access token
        เก่าออกด้วย
      summary: Refresh Token
      tags:
      - Token Management
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TokenRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/TokenRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/TokenRequest'
        required: true
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: true
                  error_message:
                    type: 'null'
                  error_code:
                    type: 'null'
                  data:
                    type: object
                    properties:
                      tokens:
                        type: object
                        properties:
                          access:
                            type: string
                          refresh:
                            type: string
                      message:
                        type: string
                        example: ต่ออายุโทเค็นสำเร็จ
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: โทเค็นไม่ถูกต้องหรือหมดอายุ
                  error_code:
                    type: integer
                    example: 1005
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
  /revoke/:
    post:
      operationId: revoke_create
      description: เพิกถอน Token โดยผู้ดูแลระบบ - ทำให้ token หมดอายุทันที
      summary: Revoke Token
      tags:
      - Token Management
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RevokeTokenRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/RevokeTokenRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/RevokeTokenRequest'
        required: true
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: true
                  error_message:
                    type: 'null'
                  error_code:
                    type: 'null'
                  data:
                    type: object
                    properties:
                      message:
                        type: string
                        example: เพิกถอนโทเค็นสำเร็จ
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: โทเค็นไม่ถูกต้อง
                  error_code:
                    type: integer
                    example: 1005
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
  /search/sector/:
    get:
      operationId: search_sector_list
      description: List all sectors with pagination and error handling
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - search
      security:
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedTcdSectorList'
          description: ''
  /search/sector/{id}/:
    get:
      operationId: search_sector_retrieve
      description: Retrieve specific sector by ID
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this tcd sector.
        required: true
      tags:
      - search
      security:
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TcdSector'
          description: ''
  /search/service/:
    get:
      operationId: search_service_list
      description: List all services with pagination and error handling
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - search
      security:
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedTcdServiceList'
          description: ''
  /search/service/{id}/:
    get:
      operationId: search_service_retrieve
      description: Retrieve specific service by ID
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this tcd service.
        required: true
      tags:
      - search
      security:
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TcdService'
          description: ''
  /search/skill/:
    get:
      operationId: search_skill_list
      description: List skills filtered by sector IDs
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - search
      security:
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedTcdSkillList'
          description: ''
  /search/skill/{id}/:
    get:
      operationId: search_skill_retrieve
      description: Retrieve specific skill by ID
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this tcd skill.
        required: true
      tags:
      - search
      security:
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TcdSkill'
          description: ''
  /staff/login/:
    post:
      operationId: staff_login_create
      description: เข้าสู่ระบบสำหรับ Staff (TcdUsers)
      summary: Staff Login
      tags:
      - Staff
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StaffLoginRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/StaffLoginRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/StaffLoginRequest'
        required: true
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: true
                  error_message:
                    type: 'null'
                  error_code:
                    type: 'null'
                  data:
                    type: object
                    properties:
                      user:
                        type: object
                      tokens:
                        type: object
                        properties:
                          access:
                            type: string
                          refresh:
                            type: string
                      session_info:
                        type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง
                  error_code:
                    type: integer
                    example: 1001
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
  /surveys/active-questionnaires/:
    get:
      operationId: surveys_active_questionnaires_retrieve
      description: |-
        Get all active questionnaires

        Returns:
        - success (bool): Whether the operation was successful
        - data (dict): Contains list of active questionnaires
      tags:
      - Surveys
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ActiveQuestionnairesResponse'
          description: ''
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SurveysResponse'
          description: ''
  /surveys/check-survey-menu/:
    get:
      operationId: surveys_check_survey_menu_retrieve
      description: |-
        Check if satisfaction survey menu should be displayed

        Query Parameters:
        - question_position_id (int, optional): Question position ID to check (default: 5)

        Returns:
        - success (bool): Whether the operation was successful
        - data (dict): Contains display status and related information
      tags:
      - Surveys
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SatisfactionSurveyMenuDisplayResponse'
          description: ''
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SurveysResponse'
          description: ''
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SurveysResponse'
          description: ''
  /surveys/questionnaire-questions/:
    get:
      operationId: surveys_questionnaire_questions_retrieve
      description: |-
        Get questions for a specific questionnaire

        Query Parameters:
        - questionnaire_id (int, required): Questionnaire ID to get questions for

        Returns:
        - success (bool): Whether the operation was successful
        - data (dict): Contains questionnaire questions
      tags:
      - Surveys
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QuestionnaireQuestionsResponse'
          description: ''
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SurveysResponse'
          description: ''
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SurveysResponse'
          description: ''
  /surveys/save-answers/:
    post:
      operationId: surveys_save_answers_create
      description: |-
        Save questionnaire answers with transaction support

        Authentication: Optional (JWT token)
        User Types: All users allowed
        - Authenticated users: Member ('M') and Consultant ('C')
        - Unauthenticated users: Guest ('G')

        Request Body:
        - questionnaire_id (int, required): Questionnaire ID
        - answers (list, required): List of answer objects with structure:
            {
                "questionnaire_question_id": int,
                "answer": int (1-5)
            }
        - additional_feedback (str, optional): Additional feedback text

        Returns:
        - success (bool): Whether the operation was successful
        - data (dict): Contains save operation results
      tags:
      - Surveys
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SaveQuestionnaireAnswersRequestRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/SaveQuestionnaireAnswersRequestRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/SaveQuestionnaireAnswersRequestRequest'
        required: true
      security:
      - CustomJWTAuth: []
      - {}
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SaveQuestionnaireAnswersResponse'
          description: ''
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SurveysResponse'
          description: ''
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SurveysResponse'
          description: ''
  /tracking/worklist/:
    get:
      operationId: tracking_worklist_retrieve
      description: ดึงรายการดำเนินการสำหรับที่ปรึกษาที่เข้าสู่ระบบ
      summary: Get Worklist
      parameters:
      - in: query
        name: page
        schema:
          type: integer
        description: 'หมายเลขหน้า (เริ่มต้น: 1)'
      - in: query
        name: page_size
        schema:
          type: integer
        description: 'จำนวนรายการต่อหน้า (เริ่มต้น: 10, สูงสุด: 100)'
      tags:
      - Tracking
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorklistResponse'
          description: ''
        '401':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_code:
                    type: integer
                    example: 1006
                  error_message:
                    type: string
                    example: ไม่มีสิทธิ์เข้าถึง
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_code:
                    type: integer
                    example: 2000
                  error_message:
                    type: string
                    example: ข้อมูลที่ส่งมาไม่ถูกต้อง
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
  /tracking/worklist/{worklist_id}/:
    get:
      operationId: tracking_worklist_retrieve_2
      description: ดึงรายละเอียดของรายการดำเนินการสำหรับที่ปรึกษาที่เข้าสู่ระบบ
      summary: Get Worklist Detail
      parameters:
      - in: path
        name: worklist_id
        schema:
          type: integer
        description: หมายเลขรายการที่ต้องการดูรายละเอียด
        required: true
      tags:
      - Tracking
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorklistDetailResponse'
          description: ''
        '401':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_code:
                    type: integer
                    example: 1006
                  error_message:
                    type: string
                    example: ไม่มีสิทธิ์เข้าถึง
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_code:
                    type: integer
                    example: 2000
                  error_message:
                    type: string
                    example: ข้อมูลที่ส่งมาไม่ถูกต้อง
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
  /tracking/worklist/{worklist_id}/logs/:
    get:
      operationId: tracking_worklist_logs_retrieve
      description: ดึงรายการ log สถานะของรายการดำเนินการสำหรับที่ปรึกษาที่เข้าสู่ระบบ
      summary: Get Worklist Logs
      parameters:
      - in: query
        name: page
        schema:
          type: integer
        description: 'หมายเลขหน้า (เริ่มต้น: 1)'
      - in: query
        name: page_size
        schema:
          type: integer
        description: 'จำนวนรายการต่อหน้า (เริ่มต้น: 10, สูงสุด: 100)'
      - in: path
        name: worklist_id
        schema:
          type: integer
        description: หมายเลขรายการที่ต้องการดู log สถานะ
        required: true
      tags:
      - Tracking
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WorklistLogResponse'
          description: ''
        '401':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_code:
                    type: integer
                    example: 1006
                  error_message:
                    type: string
                    example: ไม่มีสิทธิ์เข้าถึง
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error_code:
                    type: integer
                    example: 2000
                  error_message:
                    type: string
                    example: ข้อมูลที่ส่งมาไม่ถูกต้อง
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
  /update-token/:
    post:
      operationId: update_token_create
      description: อัปเดต token_app ของผู้ใช้ (ดึง user_id และ user_type จาก JWT token)
        - รองรับทั้ง Member และ Consultant
      summary: Update Token App
      tags:
      - Token Management
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTokenAppRequest'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UpdateTokenAppRequest'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UpdateTokenAppRequest'
        required: true
      security:
      - CustomJWTAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  error_message:
                    type: 'null'
                  error_code:
                    type: 'null'
                  data:
                    type: object
                    properties:
                      user_id:
                        type: integer
                        example: 123
                      user_type:
                        type: string
                        example: member
                      message:
                        type: string
                        example: อัปเดต token_app สำเร็จ
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '400':
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: ข้อมูลไม่ครบถ้วน
                  error_code:
                    type: integer
                    example: 1001
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '401':
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: ไม่ได้รับอนุญาต
                  error_code:
                    type: integer
                    example: 1000
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
        '404':
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: false
                  error_message:
                    type: string
                    example: ไม่พบข้อมูล
                  error_code:
                    type: integer
                    example: 1002
                  data:
                    type: object
                  api_version:
                    type: string
                    example: v.0.0.1
          description: ''
components:
  schemas:
    ActiveQuestionnairesResponse:
      type: object
      description: Serializer for active questionnaires response
      properties:
        questionnaires:
          type: array
          items:
            $ref: '#/components/schemas/Questionnaire'
        total:
          type: integer
      required:
      - questionnaires
      - total
    ChangePasswordRequest:
      type: object
      description: Serializer สำหรับการเปลี่ยนรหัสผ่าน (ไม่ต้องผ่าน OTP)
      properties:
        current_password:
          type: string
          writeOnly: true
          minLength: 4
          description: รหัสผ่านปัจจุบัน
          maxLength: 50
        new_password:
          type: string
          writeOnly: true
          minLength: 4
          description: รหัสผ่านใหม่
          maxLength: 50
        confirm_password:
          type: string
          writeOnly: true
          minLength: 4
          description: ยืนยันรหัสผ่านใหม่
          maxLength: 50
      required:
      - confirm_password
      - current_password
      - new_password
    CheckPIDRequest:
      type: object
      properties:
        pid:
          type: string
          minLength: 1
          description: หมายเลขบัตรประชาชน
      required:
      - pid
    Consultant:
      type: object
      description: Serializer สำหรับข้อมูล Consultant (ทุกฟิลด์ยกเว้น password)
      properties:
        id:
          type: integer
          readOnly: true
        consult_type:
          type: integer
          maximum: **********
          minimum: -**********
          nullable: true
        consult_type_display:
          type: string
          readOnly: true
        corporate_type_id:
          type: string
          format: decimal
          pattern: ^-?\d{0,19}(?:\.\d{0,0})?$
          nullable: true
        username:
          type: string
          maxLength: 50
        email:
          type: string
          maxLength: 50
        email_second:
          type: string
          maxLength: 50
        phone:
          type: string
          maxLength: 50
        phone_second:
          type: string
          maxLength: 50
        src:
          type: string
          nullable: true
        maker_name:
          type: string
          nullable: true
          maxLength: 50
        maker_phone:
          type: string
          nullable: true
          maxLength: 50
        maker_email:
          type: string
          nullable: true
          maxLength: 50
        pw:
          type: string
          nullable: true
          maxLength: 50
        verify:
          type: string
          nullable: true
          maxLength: 1
        verify_display:
          type: string
          readOnly: true
        reset_phone:
          type: integer
          readOnly: true
          nullable: true
        reset_phone_date:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        token_app:
          type: string
          nullable: true
          maxLength: 250
        is_notification:
          type: string
          maxLength: 1
        score:
          type: number
          format: double
          readOnly: true
        lang:
          type: string
          maxLength: 2
        is_active_matching:
          type: boolean
      required:
      - email
      - email_second
      - is_active_matching
      - is_notification
      - lang
      - phone
      - phone_second
      - username
    ConsultantDetail:
      type: object
      description: Serializer for consultant detail information
      properties:
        id:
          type: integer
        consult_type:
          type: integer
        consult_type_display:
          type: string
        corporate_type_id:
          type: integer
          nullable: true
        corporate_type_display:
          type: string
        username:
          type: string
        email:
          type: string
        email_second:
          type: string
        phone:
          type: string
        phone_second:
          type: string
        maker_name:
          type: string
          nullable: true
        maker_phone:
          type: string
          nullable: true
        maker_email:
          type: string
          nullable: true
        verify:
          type: string
        verify_display:
          type: string
        is_notification:
          type: string
        score:
          type: number
          format: double
        lang:
          type: string
        is_active_matching:
          type: boolean
        user_type:
          type: string
      required:
      - consult_type
      - consult_type_display
      - corporate_type_display
      - corporate_type_id
      - email
      - email_second
      - id
      - is_active_matching
      - is_notification
      - lang
      - maker_email
      - maker_name
      - maker_phone
      - phone
      - phone_second
      - score
      - user_type
      - username
      - verify
      - verify_display
    ConsultantExperienceItem:
      type: object
      description: Serializer for individual consultant experience item
      properties:
        project_id:
          type: integer
        project_name:
          type: string
        start_date:
          type: string
        stop_date:
          type: string
        sectors:
          type: string
          description: รายการสาขาที่เกี่ยวข้อง (คั่นด้วย ', ')
        skills:
          type: string
          description: รายการความเชี่ยวชาญที่เกี่ยวข้อง (คั่นด้วย ', ')
        services:
          type: string
          description: รายการบริการที่เกี่ยวข้อง (คั่นด้วย ', ')
      required:
      - project_id
      - project_name
      - sectors
      - services
      - skills
      - start_date
      - stop_date
    ConsultantExperienceRequestRequest:
      type: object
      description: Serializer for consultant experience request parameters
      properties:
        user_consult_id:
          type: integer
          minimum: 1
          description: ID ของที่ปรึกษา
        consult_type:
          type: integer
          minimum: 1
          description: 'ประเภทที่ปรึกษา (1: อิสระ, 2: นิติบุคคล)'
        corporate_type_id:
          type: integer
          minimum: 1
          nullable: true
          description: ID ของนิติบุคคล
      required:
      - consult_type
      - user_consult_id
    ConsultantExperienceResponse:
      type: object
      description: Serializer for consultant experience response
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/ConsultantExperienceItem'
        pagination:
          type: object
          additionalProperties: {}
          description: ข้อมูล pagination ประกอบด้วย page, page_size, total_count,
            has_next
      required:
      - pagination
      - results
    ConsultantFavoriteProjectItem:
      type: object
      description: Serializer for individual consultant favorite project item
      properties:
        project_id:
          type: integer
        project_name:
          type: string
        organization_name:
          type: string
        announcement_date:
          type: string
        favorite_status:
          type: string
          maxLength: 1
        matching_result:
          type: number
          format: double
        view_count:
          type: integer
      required:
      - announcement_date
      - favorite_status
      - matching_result
      - organization_name
      - project_id
      - project_name
      - view_count
    ConsultantFavoriteProjectsRequestRequest:
      type: object
      description: Serializer for consultant favorite projects request parameters
      properties:
        sort_by_matching:
          type: boolean
          default: false
          description: ถ้าเลือก True จะเรียงลำดับตามผลการจับคู่จาก มาก-น้อย (matching
            DESC, start_date ASC), ถ้า False จะเรียงตาม id DESC
    ConsultantFavoriteProjectsResponse:
      type: object
      description: Serializer for consultant favorite projects response
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/ConsultantFavoriteProjectItem'
        pagination:
          type: object
          additionalProperties: {}
      required:
      - pagination
      - results
    ConsultantFavoriteUpdateResponse:
      type: object
      description: Serializer for consultant favorite update response
      properties:
        id:
          type: integer
        app_project_id:
          type: integer
        user_consult_id:
          type: integer
        old_consult_favorite:
          type: string
          maxLength: 1
        new_consult_favorite:
          type: string
          maxLength: 1
        is_favorite:
          type: boolean
        action:
          type: string
        updated:
          type: boolean
      required:
      - action
      - app_project_id
      - id
      - is_favorite
      - new_consult_favorite
      - old_consult_favorite
      - updated
      - user_consult_id
    ConsultantLoginRequest:
      type: object
      properties:
        username:
          type: string
          minLength: 1
        password:
          type: string
          minLength: 1
      required:
      - password
      - username
    ConsultantMemberViewRequestRequest:
      type: object
      description: Serializer for consultant member view increment request
      properties:
        app_project_id:
          type: integer
          minimum: 1
          description: ID ของโครงการ
        user_consult_id:
          type: integer
          minimum: 1
          description: ID ของที่ปรึกษาที่เลือก
      required:
      - app_project_id
      - user_consult_id
    ConsultantMemberViewResponse:
      type: object
      description: Serializer for consultant member view increment response
      properties:
        id:
          type: integer
        app_project_id:
          type: integer
        user_consult_id:
          type: integer
        app_member_id:
          type: integer
        old_member_view:
          type: integer
        new_member_view:
          type: integer
        matching:
          type: number
          format: double
        updated:
          type: boolean
      required:
      - app_member_id
      - app_project_id
      - id
      - matching
      - new_member_view
      - old_member_view
      - updated
      - user_consult_id
    ConsultantRequest:
      type: object
      description: Serializer สำหรับข้อมูล Consultant (ทุกฟิลด์ยกเว้น password)
      properties:
        consult_type:
          type: integer
          maximum: **********
          minimum: -**********
          nullable: true
        corporate_type_id:
          type: string
          format: decimal
          pattern: ^-?\d{0,19}(?:\.\d{0,0})?$
          nullable: true
        username:
          type: string
          minLength: 1
          maxLength: 50
        email:
          type: string
          minLength: 1
          maxLength: 50
        email_second:
          type: string
          minLength: 1
          maxLength: 50
        phone:
          type: string
          minLength: 1
          maxLength: 50
        phone_second:
          type: string
          minLength: 1
          maxLength: 50
        src:
          type: string
          nullable: true
        maker_name:
          type: string
          nullable: true
          maxLength: 50
        maker_phone:
          type: string
          nullable: true
          maxLength: 50
        maker_email:
          type: string
          nullable: true
          maxLength: 50
        pw:
          type: string
          nullable: true
          maxLength: 50
        verify:
          type: string
          nullable: true
          maxLength: 1
        token_app:
          type: string
          nullable: true
          maxLength: 250
        is_notification:
          type: string
          minLength: 1
          maxLength: 1
        lang:
          type: string
          minLength: 1
          maxLength: 2
        is_active_matching:
          type: boolean
      required:
      - email
      - email_second
      - is_active_matching
      - is_notification
      - lang
      - phone
      - phone_second
      - username
    ConsultantStatus:
      type: object
      description: Serializer for consultant status information
      properties:
        verify_status:
          type: string
        verify_status_display:
          type: string
        consult_type:
          type: integer
        consult_type_display:
          type: string
        is_active_matching:
          type: boolean
        score:
          type: number
          format: double
        phone:
          type: string
        email:
          type: string
      required:
      - consult_type
      - consult_type_display
      - email
      - is_active_matching
      - phone
      - score
      - verify_status
      - verify_status_display
    ConsultantSuitableProjectItem:
      type: object
      description: Serializer for individual consultant suitable project item
      properties:
        project_id:
          type: integer
        project_name:
          type: string
        organization_name:
          type: string
        announcement_period:
          type: string
        favorite_status:
          type: string
          description: 'สถานะการถูกใจ: ''1'' = กดถูกใจ, ''0'' = ไม่ได้กดถูกใจ'
          maxLength: 1
        matching_result:
          type: number
          format: double
        view_count:
          type: integer
      required:
      - announcement_period
      - favorite_status
      - matching_result
      - organization_name
      - project_id
      - project_name
      - view_count
    ConsultantSuitableProjectsRequestRequest:
      type: object
      description: Serializer for consultant suitable projects request parameters
      properties:
        sector_id:
          type: integer
          minimum: 1
          nullable: true
          description: ID ตัวกรอง ข้อมูลสาขา
        sort_by_matching:
          type: boolean
          default: false
          description: ถ้าเลือก True จะเรียงลำดับตามผลการจับคู่จาก มาก-น้อย (matching
            DESC, start_date ASC), ถ้า False จะเรียงตาม id DESC
    ConsultantSuitableProjectsResponse:
      type: object
      description: Serializer for consultant suitable projects response
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/ConsultantSuitableProjectItem'
        pagination:
          type: object
          additionalProperties: {}
      required:
      - pagination
      - results
    ContactButton:
      type: object
      description: Serializer for contact button information
      properties:
        show:
          type: boolean
        text:
          type: string
        sent:
          type: boolean
      required:
      - show
    ContactButtonExtended:
      type: object
      description: Serializer for contact button information
      properties:
        text:
          type: string
        text_en:
          type: string
        sent:
          type: boolean
      required:
      - sent
      - text
      - text_en
    DashboardListParameterRequest:
      type: object
      description: Serializer for dashboard list parameter
      properties:
        dashboard_category_id:
          type: integer
    DashboardOverviewResponse:
      type: object
      description: Serializer for dashboard overview response
      properties:
        overview:
          $ref: '#/components/schemas/OverviewStats'
        chart_data:
          type: array
          items:
            type: object
            additionalProperties: {}
        service_info:
          type: array
          items:
            $ref: '#/components/schemas/TcdDashboard'
        project_stats:
          type: array
          items:
            $ref: '#/components/schemas/ProjectStats'
        news:
          type: array
          items:
            $ref: '#/components/schemas/TcdNews'
      required:
      - chart_data
      - news
      - overview
      - project_stats
      - service_info
    DashboardParameterRequest:
      type: object
      description: Serializer for dashboard parameter
      properties:
        project_size:
          type: integer
          default: 3
        news_size:
          type: integer
          default: 3
    Document:
      type: object
      properties:
        id:
          type: integer
        type:
          type: integer
        uuid:
          type: string
        corporate_general_data_id:
          type: integer
          nullable: true
        file_size:
          type: integer
          nullable: true
        name:
          type: string
        no_profit_general_data_id:
          type: integer
          nullable: true
        personal_general_data_id:
          type: integer
          nullable: true
        record_date:
          type: string
          format: date-time
        status:
          type: string
          nullable: true
        file_url:
          type: string
          nullable: true
      required:
      - corporate_general_data_id
      - file_size
      - file_url
      - id
      - name
      - no_profit_general_data_id
      - personal_general_data_id
      - record_date
      - status
      - type
      - uuid
    DocumentRequestRequest:
      type: object
      properties:
        general_data_id:
          type: integer
        consult_type:
          type: integer
        corporate_type_id:
          type: integer
          nullable: true
      required:
      - consult_type
      - general_data_id
    DocumentResponse:
      type: object
      properties:
        success:
          type: boolean
        error_code:
          type: string
          nullable: true
        error_message:
          type: string
          nullable: true
        data:
          type: array
          items:
            $ref: '#/components/schemas/Document'
        page:
          type: integer
        per_page:
          type: integer
        total:
          type: integer
        has_next:
          type: boolean
        api_version:
          type: string
      required:
      - api_version
      - data
      - error_code
      - error_message
      - has_next
      - page
      - per_page
      - success
      - total
    FavoriteConsultantDetails:
      type: object
      description: Serializer for favorite consultant details
      properties:
        consult_type:
          type: integer
        consult_type_display:
          type: string
        corporate_type_id:
          type: integer
          nullable: true
        corporate_type_display:
          type: string
          nullable: true
        username:
          type: string
        email:
          type: string
        phone:
          type: string
        verify:
          type: string
        verify_display:
          type: string
        score:
          type: number
          format: double
        is_active_matching:
          type: boolean
        lang:
          type: string
      required:
      - consult_type
      - consult_type_display
      - corporate_type_display
      - corporate_type_id
      - email
      - is_active_matching
      - lang
      - phone
      - score
      - username
      - verify
      - verify_display
    FavoriteConsultantItem:
      type: object
      description: Serializer for individual favorite consultant item
      properties:
        id:
          type: integer
        consultant_id:
          type: integer
        consultant_name:
          type: string
        consultant_details:
          $ref: '#/components/schemas/FavoriteConsultantDetails'
        matching_result:
          type: number
          format: double
        register_no:
          type: integer
        rating:
          type: integer
        consult_send_date:
          type: string
          nullable: true
        member_view:
          type: integer
        consult_view:
          type: integer
        member_favorite:
          type: boolean
        consult_favorite:
          type: boolean
      required:
      - consult_favorite
      - consult_send_date
      - consult_view
      - consultant_details
      - consultant_id
      - consultant_name
      - id
      - matching_result
      - member_favorite
      - member_view
      - rating
      - register_no
    FilterTypeEnum:
      enum:
      - latest
      - published
      - announcement_count
      type: string
      description: |-
        * `latest` - โครงการล่าสุด
        * `published` - โครงการที่ประกาศ
        * `announcement_count` - จำนวนที่ประกาศ
    InterestedConsultantDetails:
      type: object
      description: Serializer for interested consultant details
      properties:
        consult_type:
          type: integer
        consult_type_display:
          type: string
        corporate_type_id:
          type: integer
          nullable: true
        corporate_type_display:
          type: string
          nullable: true
        username:
          type: string
        email:
          type: string
        phone:
          type: string
        verify:
          type: string
        verify_display:
          type: string
        score:
          type: number
          format: double
        is_active_matching:
          type: boolean
        lang:
          type: string
      required:
      - consult_type
      - consult_type_display
      - corporate_type_display
      - corporate_type_id
      - email
      - is_active_matching
      - lang
      - phone
      - score
      - username
      - verify
      - verify_display
    InterestedConsultantItem:
      type: object
      description: Serializer for individual interested consultant item
      properties:
        id:
          type: integer
        consultant_id:
          type: integer
        consultant_name:
          type: string
        consultant_details:
          $ref: '#/components/schemas/InterestedConsultantDetails'
        matching_result:
          type: number
          format: double
        register_no:
          type: integer
        rating:
          type: integer
        consult_send_date:
          type: string
          nullable: true
        member_view:
          type: integer
        consult_view:
          type: integer
        member_favorite:
          type: boolean
        consult_favorite:
          type: boolean
      required:
      - consult_favorite
      - consult_send_date
      - consult_view
      - consultant_details
      - consultant_id
      - consultant_name
      - id
      - matching_result
      - member_favorite
      - member_view
      - rating
      - register_no
    MatchingResult:
      type: object
      description: Serializer for matching result information
      properties:
        matching_score:
          type: number
          format: double
          nullable: true
        consult_send:
          type: integer
          nullable: true
      required:
      - consult_send
      - matching_score
    Member:
      type: object
      description: Serializer สำหรับข้อมูล Member (ทุกฟิลด์ยกเว้น password)
      properties:
        id:
          type: integer
          readOnly: true
        name:
          type: string
          nullable: true
          maxLength: 255
        first_name:
          type: string
          maxLength: 255
        last_name:
          type: string
          maxLength: 255
        email:
          type: string
          maxLength: 50
        phone:
          type: string
          nullable: true
          maxLength: 50
        identity_card_no:
          type: string
          nullable: true
          maxLength: 20
        src:
          type: string
          nullable: true
        fb_id:
          type: string
          nullable: true
          maxLength: 50
        google_id:
          type: string
          nullable: true
          maxLength: 50
        apple_id:
          type: string
          nullable: true
          maxLength: 50
        username:
          type: string
          maxLength: 50
        app_mas_member_type:
          type: integer
          nullable: true
        app_mas_government_sector:
          type: integer
          nullable: true
        app_mas_government_sector_other:
          type: string
          nullable: true
          maxLength: 255
        app_mas_ministry:
          type: integer
          nullable: true
        app_mas_ministry_other:
          type: string
          nullable: true
          maxLength: 255
        app_mas_department:
          type: integer
          nullable: true
        app_mas_department_other:
          type: string
          nullable: true
          maxLength: 255
        website:
          type: string
          nullable: true
          maxLength: 255
        create_date:
          type: string
          format: date-time
          readOnly: true
        token_app:
          type: string
          nullable: true
          maxLength: 250
        is_notification:
          type: string
          maxLength: 1
        checklogin:
          type: integer
          readOnly: true
        lockout_end_date:
          type: string
          format: date-time
          readOnly: true
          nullable: true
        status:
          type: string
          maxLength: 1
        status_display:
          type: string
          readOnly: true
        lang:
          type: string
          maxLength: 2
      required:
      - email
      - first_name
      - is_notification
      - lang
      - last_name
      - status
      - username
    MemberLoginRequest:
      type: object
      properties:
        username:
          type: string
          minLength: 1
        password:
          type: string
          minLength: 1
      required:
      - password
      - username
    MemberProjectSearchItem:
      type: object
      description: Serializer for individual member project search item
      properties:
        project_id:
          type: integer
        project_name:
          type: string
        announcement_date:
          type: string
        status:
          type: integer
        status_name:
          type: string
        matching_count:
          type: integer
        interested_count:
          type: integer
        view_count:
          type: integer
      required:
      - announcement_date
      - interested_count
      - matching_count
      - project_id
      - project_name
      - status
      - status_name
      - view_count
    MemberProjectSearchRequestRequest:
      type: object
      description: Serializer for member project search request parameters
      properties:
        project_name:
          type: string
          maxLength: 500
        announcement_start_date:
          type: string
          format: date
        announcement_end_date:
          type: string
          format: date
        filter_type:
          allOf:
          - $ref: '#/components/schemas/FilterTypeEnum'
          default: latest
          description: |-
            เลือกเงื่อนไขการกรองข้อมูล: latest=โครงการล่าสุด, published=โครงการที่ประกาศ, announcement_count=จำนวนที่ประกาศ

            * `latest` - โครงการล่าสุด
            * `published` - โครงการที่ประกาศ
            * `announcement_count` - จำนวนที่ประกาศ
    MemberProjectSearchResponse:
      type: object
      description: Serializer for member project search response
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/MemberProjectSearchItem'
        pagination:
          type: object
          additionalProperties: {}
      required:
      - pagination
      - results
    MemberRegistrationRequest:
      type: object
      description: |-
        Serializer สำหรับการสมัครสมาชิก Member ใหม่
        รองรับฟิลด์ทั้งหมดของ TcdAppMember และ OTP token validation
      properties:
        username:
          type: string
          minLength: 4
          maxLength: 50
        email:
          type: string
          format: email
          minLength: 1
          maxLength: 50
        password:
          type: string
          writeOnly: true
          minLength: 4
          maxLength: 50
        confirm_password:
          type: string
          writeOnly: true
          minLength: 4
          maxLength: 50
        first_name:
          type: string
          minLength: 1
          maxLength: 255
        last_name:
          type: string
          minLength: 1
          maxLength: 255
        phone:
          type: string
          minLength: 1
          maxLength: 50
        name:
          type: string
          nullable: true
          maxLength: 255
        identity_card_no:
          type: string
          nullable: true
          maxLength: 20
        website:
          type: string
          nullable: true
          maxLength: 255
        fb_id:
          type: string
          nullable: true
          maxLength: 50
        google_id:
          type: string
          nullable: true
          maxLength: 50
        apple_id:
          type: string
          nullable: true
          maxLength: 50
        app_mas_member_type_id:
          type: integer
          nullable: true
        app_mas_government_sector_id:
          type: integer
          nullable: true
        app_mas_ministry_id:
          type: integer
          nullable: true
        app_mas_department_id:
          type: integer
          nullable: true
        app_mas_government_sector_other:
          type: string
          nullable: true
          maxLength: 255
        app_mas_ministry_other:
          type: string
          nullable: true
          maxLength: 255
        app_mas_department_other:
          type: string
          nullable: true
          maxLength: 255
        is_notification:
          type: string
          minLength: 1
          maxLength: 1
        lang:
          type: string
          minLength: 1
          default: th
          maxLength: 2
        status:
          type: string
          minLength: 1
          default: '1'
          maxLength: 1
        otp_token:
          type: string
          minLength: 1
          description: Token ที่ได้จากการ verify OTP
      required:
      - confirm_password
      - email
      - first_name
      - is_notification
      - last_name
      - otp_token
      - password
      - phone
      - username
    MemberRequest:
      type: object
      description: Serializer สำหรับข้อมูล Member (ทุกฟิลด์ยกเว้น password)
      properties:
        name:
          type: string
          nullable: true
          maxLength: 255
        first_name:
          type: string
          minLength: 1
          maxLength: 255
        last_name:
          type: string
          minLength: 1
          maxLength: 255
        email:
          type: string
          minLength: 1
          maxLength: 50
        phone:
          type: string
          nullable: true
          maxLength: 50
        identity_card_no:
          type: string
          nullable: true
          maxLength: 20
        src:
          type: string
          nullable: true
        fb_id:
          type: string
          nullable: true
          maxLength: 50
        google_id:
          type: string
          nullable: true
          maxLength: 50
        apple_id:
          type: string
          nullable: true
          maxLength: 50
        username:
          type: string
          minLength: 1
          maxLength: 50
        app_mas_member_type:
          type: integer
          nullable: true
        app_mas_government_sector:
          type: integer
          nullable: true
        app_mas_government_sector_other:
          type: string
          nullable: true
          maxLength: 255
        app_mas_ministry:
          type: integer
          nullable: true
        app_mas_ministry_other:
          type: string
          nullable: true
          maxLength: 255
        app_mas_department:
          type: integer
          nullable: true
        app_mas_department_other:
          type: string
          nullable: true
          maxLength: 255
        website:
          type: string
          nullable: true
          maxLength: 255
        token_app:
          type: string
          nullable: true
          maxLength: 250
        is_notification:
          type: string
          minLength: 1
          maxLength: 1
        status:
          type: string
          minLength: 1
          maxLength: 1
        lang:
          type: string
          minLength: 1
          maxLength: 2
      required:
      - email
      - first_name
      - is_notification
      - lang
      - last_name
      - status
      - username
    OrganizationDetail:
      type: object
      description: Serializer for organization information
      properties:
        name:
          type: string
        phone:
          type: string
        email:
          type: string
        type:
          type: string
        website:
          type: string
      required:
      - email
      - name
      - phone
      - type
      - website
    OrganizationDetailExtended:
      type: object
      description: Serializer for organization information in extended project detail
      properties:
        name:
          type: string
        phone:
          type: string
          nullable: true
        email:
          type: string
          nullable: true
        type:
          type: string
        website:
          type: string
          nullable: true
      required:
      - email
      - name
      - phone
      - type
      - website
    OverviewStats:
      type: object
      description: Serializer for overview statistics
      properties:
        total_projects:
          type: integer
        total_members:
          type: integer
        total_consultants:
          type: integer
      required:
      - total_consultants
      - total_members
      - total_projects
    PaginatedTcdAppFaqList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/TcdAppFaq'
    PaginatedTcdAppFaqcategoryList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/TcdAppFaqcategory'
    PaginatedTcdAppMasDepartmentList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/TcdAppMasDepartment'
    PaginatedTcdAppMasGovernmentSectorList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/TcdAppMasGovernmentSector'
    PaginatedTcdAppMasMemberTypeList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/TcdAppMasMemberType'
    PaginatedTcdAppMasMinistryList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/TcdAppMasMinistry'
    PaginatedTcdAppNotificationList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/TcdAppNotification'
    PaginatedTcdNewsList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/TcdNews'
    PaginatedTcdSectorList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/TcdSector'
    PaginatedTcdServiceList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/TcdService'
    PaginatedTcdSkillList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/TcdSkill'
    PasswordResetRequestRequest:
      type: object
      description: Serializer สำหรับการขอรีเซ็ตรหัสผ่าน
      properties:
        email:
          type: string
          format: email
          minLength: 1
          description: อีเมลของผู้ใช้ที่ต้องการรีเซ็ตรหัสผ่าน
      required:
      - email
    PasswordResetUpdatePasswordRequest:
      type: object
      description: Serializer สำหรับการอัปเดตรหัสผ่านใหม่
      properties:
        email:
          type: string
          format: email
          minLength: 1
          description: อีเมลของผู้ใช้
        new_password:
          type: string
          writeOnly: true
          minLength: 4
          description: รหัสผ่านใหม่
          maxLength: 50
        confirm_password:
          type: string
          writeOnly: true
          minLength: 4
          description: ยืนยันรหัสผ่านใหม่
          maxLength: 50
        verified_token:
          type: string
          minLength: 1
          description: Token ที่ได้จากการ verify OTP สำเร็จ
      required:
      - confirm_password
      - email
      - new_password
      - verified_token
    PasswordResetVerifyOTPRequest:
      type: object
      description: Serializer สำหรับการตรวจสอบ OTP ในการรีเซ็ตรหัสผ่าน
      properties:
        email:
          type: string
          format: email
          minLength: 1
          description: อีเมลของผู้ใช้
        otp:
          type: string
          minLength: 6
          description: รหัส OTP 6 หลัก
          maxLength: 6
        ref_code:
          type: string
          minLength: 6
          description: รหัสอ้างอิง 6 หลัก
          maxLength: 6
        otp_token:
          type: string
          minLength: 1
          description: Token จากการขอ OTP
      required:
      - email
      - otp
      - otp_token
      - ref_code
    PatchedConsultantRequest:
      type: object
      description: Serializer สำหรับข้อมูล Consultant (ทุกฟิลด์ยกเว้น password)
      properties:
        consult_type:
          type: integer
          maximum: **********
          minimum: -**********
          nullable: true
        corporate_type_id:
          type: string
          format: decimal
          pattern: ^-?\d{0,19}(?:\.\d{0,0})?$
          nullable: true
        username:
          type: string
          minLength: 1
          maxLength: 50
        email:
          type: string
          minLength: 1
          maxLength: 50
        email_second:
          type: string
          minLength: 1
          maxLength: 50
        phone:
          type: string
          minLength: 1
          maxLength: 50
        phone_second:
          type: string
          minLength: 1
          maxLength: 50
        src:
          type: string
          nullable: true
        maker_name:
          type: string
          nullable: true
          maxLength: 50
        maker_phone:
          type: string
          nullable: true
          maxLength: 50
        maker_email:
          type: string
          nullable: true
          maxLength: 50
        pw:
          type: string
          nullable: true
          maxLength: 50
        verify:
          type: string
          nullable: true
          maxLength: 1
        token_app:
          type: string
          nullable: true
          maxLength: 250
        is_notification:
          type: string
          minLength: 1
          maxLength: 1
        lang:
          type: string
          minLength: 1
          maxLength: 2
        is_active_matching:
          type: boolean
    PatchedMemberRequest:
      type: object
      description: Serializer สำหรับข้อมูล Member (ทุกฟิลด์ยกเว้น password)
      properties:
        name:
          type: string
          nullable: true
          maxLength: 255
        first_name:
          type: string
          minLength: 1
          maxLength: 255
        last_name:
          type: string
          minLength: 1
          maxLength: 255
        email:
          type: string
          minLength: 1
          maxLength: 50
        phone:
          type: string
          nullable: true
          maxLength: 50
        identity_card_no:
          type: string
          nullable: true
          maxLength: 20
        src:
          type: string
          nullable: true
        fb_id:
          type: string
          nullable: true
          maxLength: 50
        google_id:
          type: string
          nullable: true
          maxLength: 50
        apple_id:
          type: string
          nullable: true
          maxLength: 50
        username:
          type: string
          minLength: 1
          maxLength: 50
        app_mas_member_type:
          type: integer
          nullable: true
        app_mas_government_sector:
          type: integer
          nullable: true
        app_mas_government_sector_other:
          type: string
          nullable: true
          maxLength: 255
        app_mas_ministry:
          type: integer
          nullable: true
        app_mas_ministry_other:
          type: string
          nullable: true
          maxLength: 255
        app_mas_department:
          type: integer
          nullable: true
        app_mas_department_other:
          type: string
          nullable: true
          maxLength: 255
        website:
          type: string
          nullable: true
          maxLength: 255
        token_app:
          type: string
          nullable: true
          maxLength: 250
        is_notification:
          type: string
          minLength: 1
          maxLength: 1
        status:
          type: string
          minLength: 1
          maxLength: 1
        lang:
          type: string
          minLength: 1
          maxLength: 2
    PatchedUpdateMemberLangRequest:
      type: object
      description: Serializer สำหรับการอัปเดตภาษาของสมาชิก Member โดยไม่ต้องตรวจสอบรหัสผ่าน
      properties:
        lang:
          type: string
          minLength: 1
          description: ภาษาที่ต้องการเปลี่ยน ('th' หรือ 'en')
          maxLength: 2
    PaymentDetailById:
      type: object
      description: Serializer for individual payment detail by ID
      properties:
        id:
          type: integer
          description: Payment ID
        request_type:
          type: integer
          description: ประเภทคำขอ
        number:
          type: string
          description: หมายเลข
        ref2:
          type: string
          description: รหัสอ้างอิง 2
        biller_id:
          type: string
          description: Biller ID
        bill_no:
          type: string
          description: เลขที่ใบแจ้งการชำระ
        cgd_ref1:
          type: string
          description: รหัสอ้างอิง (Ref.) 1
        cgd_ref2:
          type: string
          description: รหัสอ้างอิง (Ref.) 2
        barcode_string:
          type: string
          description: Barcode String
        qrcode_string:
          type: string
          description: QR Code String
        response_pmt1:
          type: string
          description: Response PMT 1
        response_pmt2:
          type: string
          description: Response PMT 2
        src_qrcode:
          type: string
          description: Source QR Code
        src_payin:
          type: string
          description: Source Pay In
        amount:
          type: string
          description: จำนวนเงิน
        pay_amount:
          type: string
          description: จำนวนเงินที่ชำระ
        worklist_id:
          type: integer
          description: Worklist ID
        user_consult_id:
          type: integer
          description: User Consult ID
        src:
          type: string
          description: Source
        create_date:
          type: string
          description: วันที่เรียกเก็บ (d MMM yyyy HH:mm)
        expire_date:
          type: string
          description: วันที่ครบกำหนด (d MMM yyyy HH:mm)
        status:
          type: string
          description: สถานะ
        pay_date:
          type: string
          description: วันที่ดำเนินการ
        amount_formatted:
          type: string
          description: ยอดชำระทั้งหมด (รูปแบบเงิน)
        show_payment_proof_button:
          type: boolean
          description: แสดงปุ่มหลักฐานการชำระ
        show_payment_button:
          type: boolean
          description: แสดงปุ่มชำระค่าธรรมเนียม
        status_text:
          type: string
          description: ข้อความสถานะ
        description:
          type: string
          description: รายละเอียด
      required:
      - amount
      - amount_formatted
      - barcode_string
      - bill_no
      - biller_id
      - cgd_ref1
      - cgd_ref2
      - create_date
      - description
      - expire_date
      - id
      - number
      - pay_amount
      - pay_date
      - qrcode_string
      - ref2
      - request_type
      - response_pmt1
      - response_pmt2
      - show_payment_button
      - show_payment_proof_button
      - src
      - src_payin
      - src_qrcode
      - status
      - status_text
      - user_consult_id
      - worklist_id
    PaymentDetailByIdResponse:
      type: object
      description: Serializer for payment detail by ID response
      properties:
        success:
          type: boolean
          description: สถานะการดำเนินการ
        error_code:
          type: string
          nullable: true
          description: รหัสข้อผิดพลาด
        error_message:
          type: string
          nullable: true
          description: ข้อความข้อผิดพลาด
        data:
          allOf:
          - $ref: '#/components/schemas/PaymentDetailById'
          description: ข้อมูลการชำระ
        api_version:
          type: string
          description: เวอร์ชัน API
      required:
      - api_version
      - data
      - error_code
      - error_message
      - success
    PaymentDetailItem:
      type: object
      description: Serializer for individual payment detail item
      properties:
        bill_no:
          type: string
          description: เลขที่ใบแจ้งการชำระ
        cgd_ref1:
          type: string
          description: รหัสอ้างอิง (Ref.) 1
        create_date:
          type: string
          description: วันที่เรียกเก็บ (d MMM yyyy HH:mm)
        expire_date:
          type: string
          description: วันที่ครบกำหนด (d MMM yyyy HH:mm)
        status_text:
          type: string
          description: สถานะ
        description:
          type: string
          description: รายละเอียด
        pay_date:
          type: string
          description: วันที่ดำเนินการ
        amount_formatted:
          type: string
          description: ยอดชำระทั้งหมด
        show_payment_proof_button:
          type: boolean
          description: แสดงปุ่มหลักฐานการชำระ
        show_payment_button:
          type: boolean
          description: แสดงปุ่มชำระค่าธรรมเนียม
      required:
      - amount_formatted
      - bill_no
      - cgd_ref1
      - create_date
      - description
      - expire_date
      - pay_date
      - show_payment_button
      - show_payment_proof_button
      - status_text
    PaymentDetailListResponse:
      type: object
      description: Serializer for payment detail list response
      properties:
        payments:
          type: array
          items:
            $ref: '#/components/schemas/PaymentDetailItem'
          description: รายการข้อมูลการชำระ
        page:
          type: integer
          description: หน้าปัจจุบัน
        per_page:
          type: integer
          description: จำนวนรายการต่อหน้า
        total:
          type: integer
          description: จำนวนรายการทั้งหมด
        has_next:
          type: boolean
          description: มีหน้าถัดไปหรือไม่
      required:
      - has_next
      - page
      - payments
      - per_page
      - total
    ProjectCountRequestRequest:
      type: object
      description: Serializer for project count request parameters with dynamic filtering
      properties:
        project_name:
          type: string
          maxLength: 500
        organization_name:
          type: string
          maxLength: 255
        sector_ids:
          type: string
        project_period_start:
          type: string
          format: date
        project_period_end:
          type: string
          format: date
        announcement_start_date:
          type: string
          format: date
        announcement_end_date:
          type: string
          format: date
    ProjectCountResponse:
      type: object
      description: Serializer for project count response
      properties:
        count:
          type: integer
        filters_applied:
          type: object
          additionalProperties: {}
      required:
      - count
      - filters_applied
    ProjectDetailExtendedRequestRequest:
      type: object
      description: Serializer for project detail extended request parameters
      properties:
        app_project_id:
          type: integer
          minimum: 1
          description: ID ของโครงการที่ต้องการดูรายละเอียด
      required:
      - app_project_id
    ProjectDetailExtendedResponse:
      type: object
      description: Serializer for extended project detail response with all master
        data relationships
      properties:
        project_id:
          type: integer
        project_name:
          type: string
        view_count:
          type: integer
        purpose:
          type: string
          nullable: true
          description: วัตถุประสงค์โครงการ
        activity:
          type: string
          nullable: true
          description: ขอบเขตการดำเนินงาน
        project_period:
          type: string
          nullable: true
          description: ระยะเวลาโครงการ
        announcement_period:
          type: string
          nullable: true
          description: วันที่ประกาศโครงการ
        keyword:
          type: string
          nullable: true
          description: คำค้นหา (Keyword)
        document_url:
          type: string
          nullable: true
          description: ปุ่ม ดาวน์โหลดเอกสาร
        organization:
          allOf:
          - $ref: '#/components/schemas/OrganizationDetailExtended'
          nullable: true
        matching_result:
          allOf:
          - $ref: '#/components/schemas/MatchingResult'
          nullable: true
          description: ผลจับคู่ - แสดงเมื่อเป็นที่ปรึกษาและมีการ matching
        contact_button:
          allOf:
          - $ref: '#/components/schemas/ContactButtonExtended'
          nullable: true
          description: ปุ่ม ส่งข้อมูลติดต่อ - แสดงเมื่อเป็นที่ปรึกษาและมีการ matching
        sectors_skills:
          type: array
          items:
            $ref: '#/components/schemas/SectorDetailExtended'
          description: ข้อมูลสาขา ความเชี่ยวชาญ
        services:
          type: array
          items:
            $ref: '#/components/schemas/ServiceDetailExtended'
          description: การบริการ
        project_sector_count:
          type: string
          nullable: true
          description: จำนวนโครงการที่สอดคล้องกับสาขา
        consultant_experience:
          type: string
          nullable: true
          description: ประสบการณ์ที่ปรึกษา
        project_cost:
          type: string
          nullable: true
          description: มูลค่าสัญญาจ้างที่ปรึกษา
        project_number:
          type: string
          nullable: true
          description: จำนวนโครงการ
        project_type:
          type: string
          nullable: true
          description: ประเภทโครงการ
        consultant_number:
          type: string
          nullable: true
          description: จำนวนบุคลากรที่ปรึกษา
        consultant_type:
          type: string
          nullable: true
          description: ประเภทที่ปรึกษา
        consultant_rating:
          type: string
          nullable: true
          description: ระดับที่ปรึกษา
        consultant_certificate:
          type: string
          nullable: true
          description: หนังสือรับรองการขึ้นทะเบียน
        matching_result_master:
          type: string
          nullable: true
          description: ผลการจับคู่
      required:
      - activity
      - announcement_period
      - consultant_certificate
      - consultant_experience
      - consultant_number
      - consultant_rating
      - consultant_type
      - contact_button
      - document_url
      - keyword
      - matching_result
      - matching_result_master
      - organization
      - project_cost
      - project_id
      - project_name
      - project_number
      - project_period
      - project_sector_count
      - project_type
      - purpose
      - sectors_skills
      - services
      - view_count
    ProjectDetailResponse:
      type: object
      description: Serializer for project detail response
      properties:
        project_name:
          type: string
        view_count:
          type: integer
        purpose:
          type: string
        activity_scope:
          type: string
        project_period:
          type: string
        announcement_period:
          type: string
        keyword:
          type: string
        document_url:
          type: string
          nullable: true
          description: ปุ่ม ดาวน์โหลดเอกสาร
        organization:
          $ref: '#/components/schemas/OrganizationDetail'
        consultant_experience:
          type: string
        matching_result_type:
          type: string
        contract_value:
          type: string
        project_count:
          type: string
        project_sector_count:
          type: string
        project_type:
          type: string
        consultant_count:
          type: string
        consultant_type:
          type: string
        consultant_level:
          type: string
        registration_certificate:
          type: string
        sectors:
          type: array
          items:
            $ref: '#/components/schemas/SectorDetail'
        services:
          type: array
          items:
            $ref: '#/components/schemas/ServiceDetail'
        matching_result:
          type: number
          format: double
          nullable: true
        contact_button:
          $ref: '#/components/schemas/ContactButton'
      required:
      - activity_scope
      - announcement_period
      - consultant_count
      - consultant_experience
      - consultant_level
      - consultant_type
      - contact_button
      - contract_value
      - keyword
      - matching_result_type
      - organization
      - project_count
      - project_name
      - project_period
      - project_sector_count
      - project_type
      - purpose
      - registration_certificate
      - sectors
      - services
      - view_count
    ProjectFavoriteConsultantsRequestRequest:
      type: object
      description: Serializer for project favorite consultants request
      properties:
        sort_by_matching:
          type: boolean
          default: false
          description: ถ้าเลือก True จะเรียงลำดับตามผลการจับคู่จาก มาก-น้อย (matching
            DESC, rating ASC, register_no ASC), ถ้า False จะเรียงตาม id DESC
    ProjectFavoriteConsultantsResponse:
      type: object
      description: Serializer for project favorite consultants response
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/FavoriteConsultantItem'
        pagination:
          type: object
          additionalProperties: {}
        project_info:
          type: object
          additionalProperties: {}
      required:
      - pagination
      - project_info
      - results
    ProjectInterestStatusRequestRequest:
      type: object
      description: Serializer for project interest status request parameters
      properties:
        app_project_id:
          type: integer
          minimum: 1
          description: ID ของโครงการที่เลือก
      required:
      - app_project_id
    ProjectInterestStatusResponse:
      type: object
      description: Serializer for project interest status response
      properties:
        id:
          type: integer
        app_project_id:
          type: integer
        user_consult_id:
          type: integer
        app_member_id:
          type: integer
        consult_send:
          type: integer
        is_interested:
          type: boolean
      required:
      - app_member_id
      - app_project_id
      - consult_send
      - id
      - is_interested
      - user_consult_id
    ProjectInterestUpdateRequestRequest:
      type: object
      description: Serializer for project interest update request parameters
      properties:
        app_project_id:
          type: integer
          minimum: 1
          description: ID ของโครงการที่เลือก
      required:
      - app_project_id
    ProjectInterestUpdateResponse:
      type: object
      description: Serializer for project interest update response
      properties:
        id:
          type: integer
        app_project_id:
          type: integer
        user_consult_id:
          type: integer
        app_member_id:
          type: integer
        old_consult_send:
          type: integer
        new_consult_send:
          type: integer
        is_interested:
          type: boolean
        action:
          type: string
        updated:
          type: boolean
      required:
      - action
      - app_member_id
      - app_project_id
      - id
      - is_interested
      - new_consult_send
      - old_consult_send
      - updated
      - user_consult_id
    ProjectInterestedConsultantsRequestRequest:
      type: object
      description: Serializer for project interested consultants request
      properties:
        sort_by_matching:
          type: boolean
          default: false
          description: ถ้าเลือก True จะเรียงลำดับตามผลการจับคู่จาก มาก-น้อย (matching
            DESC, rating ASC, register_no ASC), ถ้า False จะเรียงตาม id DESC
    ProjectInterestedConsultantsResponse:
      type: object
      description: Serializer for project interested consultants response
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/InterestedConsultantItem'
        pagination:
          type: object
          additionalProperties: {}
        project_info:
          type: object
          additionalProperties: {}
      required:
      - pagination
      - project_info
      - results
    ProjectMemberFavoriteRequestRequest:
      type: object
      description: Serializer for project member favorite request parameters
      properties:
        app_project_id:
          type: integer
          minimum: 1
          description: ID ของโครงการที่เลือก
        user_consult_id:
          type: integer
          minimum: 1
          description: ID ของที่ปรึกษาที่เลือก
      required:
      - app_project_id
      - user_consult_id
    ProjectMemberFavoriteUpdateResponse:
      type: object
      description: Serializer for project member favorite update response
      properties:
        id:
          type: integer
        app_project_id:
          type: integer
        user_consult_id:
          type: integer
        app_member_id:
          type: integer
        old_member_favorite:
          type: string
          maxLength: 1
        new_member_favorite:
          type: string
          maxLength: 1
        is_favorite:
          type: boolean
        action:
          type: string
        updated:
          type: boolean
      required:
      - action
      - app_member_id
      - app_project_id
      - id
      - is_favorite
      - new_member_favorite
      - old_member_favorite
      - updated
      - user_consult_id
    ProjectSearchRequestRequest:
      type: object
      description: Enhanced serializer for project search request with comprehensive
        filtering
      properties:
        project_name:
          type: string
          maxLength: 500
        organization_name:
          type: string
          maxLength: 255
        keyword:
          type: string
          maxLength: 255
        sector_ids:
          type: string
        project_period_start:
          type: string
          format: date
        project_period_end:
          type: string
          format: date
        announcement_start_date:
          type: string
          format: date
        announcement_end_date:
          type: string
          format: date
        min_view_count:
          type: integer
          minimum: 0
        max_view_count:
          type: integer
          minimum: 0
        has_matching_results:
          type: boolean
        sort_by:
          allOf:
          - $ref: '#/components/schemas/SortByEnum'
          default: -start_date
    ProjectStats:
      type: object
      description: Serializer for project statistics
      properties:
        id:
          type: integer
        name:
          type: string
        period_start:
          type: string
          format: date-time
        period_end:
          type: string
          format: date-time
        start_date:
          type: string
          format: date-time
        end_date:
          type: string
          format: date-time
        create_date:
          type: string
          format: date-time
        update_date:
          type: string
          format: date-time
      required:
      - create_date
      - end_date
      - id
      - name
      - period_end
      - period_start
      - start_date
      - update_date
    ProjectViewIncrementResponse:
      type: object
      description: Serializer for project view increment response
      properties:
        project_id:
          type: integer
        old_view_count:
          type: integer
        new_view_count:
          type: integer
        updated:
          type: boolean
        document_download_url:
          type: string
          nullable: true
      required:
      - new_view_count
      - old_view_count
      - project_id
      - updated
    Questionnaire:
      type: object
      description: Serializer for questionnaire data
      properties:
        id:
          type: integer
        name_th:
          type: string
        name_en:
          type: string
        count:
          type: integer
        createdate:
          type: string
          format: date-time
        createuser:
          type: integer
        status:
          type: string
      required:
      - count
      - createdate
      - createuser
      - id
      - name_en
      - name_th
      - status
    QuestionnaireAnswerRequest:
      type: object
      description: Serializer for individual questionnaire answer
      properties:
        question_id:
          type: integer
        answer:
          type: string
          minLength: 1
      required:
      - answer
      - question_id
    QuestionnaireQuestion:
      type: object
      description: Serializer for questionnaire question data
      properties:
        id:
          type: integer
        question_th:
          type: string
        question_en:
          type: string
        order:
          type: integer
      required:
      - id
      - order
      - question_en
      - question_th
    QuestionnaireQuestionsResponse:
      type: object
      description: Serializer for questionnaire questions response
      properties:
        questionnaire_id:
          type: integer
        questions:
          type: array
          items:
            $ref: '#/components/schemas/QuestionnaireQuestion'
        total:
          type: integer
      required:
      - questionnaire_id
      - questions
      - total
    RevokeTokenRequest:
      type: object
      description: Serializer สำหรับการเพิกถอน token
      properties:
        token:
          type: string
          minLength: 1
          description: Token ที่ต้องการเพิกถอน (access หรือ refresh token)
      required:
      - token
    SatisfactionSurveyMenuDisplayResponse:
      type: object
      description: Serializer for satisfaction survey menu display response
      properties:
        question_position_id:
          type: integer
        should_display:
          type: boolean
        row_count:
          type: integer
        questionnaire_id:
          type: integer
        questionnaire_name:
          type: string
      required:
      - question_position_id
      - questionnaire_id
      - questionnaire_name
      - row_count
      - should_display
    SaveQuestionnaireAnswersRequestRequest:
      type: object
      description: Serializer for save questionnaire answers request
      properties:
        questionnaire_id:
          type: integer
        answers:
          type: array
          items:
            $ref: '#/components/schemas/QuestionnaireAnswerRequest'
        additional_feedback:
          type: string
      required:
      - answers
      - questionnaire_id
    SaveQuestionnaireAnswersResponse:
      type: object
      description: Serializer for save questionnaire answers response
      properties:
        questionnaire_id:
          type: integer
        answers_saved:
          type: integer
        member_type:
          type: string
        timestamp:
          type: string
          format: date-time
      required:
      - answers_saved
      - member_type
      - questionnaire_id
      - timestamp
    SectorDetail:
      type: object
      description: Serializer for sector detail information
      properties:
        sector_code:
          type: string
        sector_name_th:
          type: string
        sector_name_en:
          type: string
        sector_display:
          type: string
        skills:
          type: array
          items:
            $ref: '#/components/schemas/SectorSkill'
      required:
      - sector_code
      - sector_display
      - sector_name_en
      - sector_name_th
      - skills
    SectorDetailExtended:
      type: object
      description: Serializer for sector information with skills
      properties:
        code:
          type: string
        name_th:
          type: string
        name_en:
          type: string
        display:
          type: string
        skills:
          type: array
          items:
            $ref: '#/components/schemas/SkillDetailExtended'
      required:
      - code
      - display
      - name_en
      - name_th
      - skills
    SectorSkill:
      type: object
      description: Serializer for sector and skill information
      properties:
        skill_code:
          type: string
        skill_name_th:
          type: string
        skill_name_en:
          type: string
        skill_display:
          type: string
      required:
      - skill_code
      - skill_display
      - skill_name_en
      - skill_name_th
    ServiceDetail:
      type: object
      description: Serializer for service information
      properties:
        service_code:
          type: string
        service_name_th:
          type: string
        service_name_en:
          type: string
        service_display:
          type: string
      required:
      - service_code
      - service_display
      - service_name_en
      - service_name_th
    ServiceDetailExtended:
      type: object
      description: Serializer for service information
      properties:
        code:
          type: string
        name_th:
          type: string
        name_en:
          type: string
        display:
          type: string
      required:
      - code
      - display
      - name_en
      - name_th
    SkillDetailExtended:
      type: object
      description: Serializer for skill information
      properties:
        code:
          type: string
        name_th:
          type: string
        name_en:
          type: string
        display:
          type: string
      required:
      - code
      - display
      - name_en
      - name_th
    SortByEnum:
      enum:
      - name
      - -name
      - start_date
      - -start_date
      - end_date
      - -end_date
      - view_count
      - -view_count
      - matching
      - -matching
      type: string
      description: |-
        * `name` - name
        * `-name` - -name
        * `start_date` - start_date
        * `-start_date` - -start_date
        * `end_date` - end_date
        * `-end_date` - -end_date
        * `view_count` - view_count
        * `-view_count` - -view_count
        * `matching` - matching
        * `-matching` - -matching
    StaffLoginRequest:
      type: object
      description: Serializer สำหรับการเข้าสู่ระบบของ Staff (TcdUsers)
      properties:
        username:
          type: string
          minLength: 1
        password:
          type: string
          minLength: 1
      required:
      - password
      - username
    SuitableConsultantItem:
      type: object
      description: Serializer for individual suitable consultant item
      properties:
        consultant_id:
          type: integer
        consultant_name:
          type: string
        consultant_status:
          $ref: '#/components/schemas/ConsultantStatus'
        favorite_status:
          type: integer
        matching_result:
          type: number
          format: double
        register_no:
          type: integer
        rating:
          type: integer
        consultant_detail:
          $ref: '#/components/schemas/ConsultantDetail'
      required:
      - consultant_detail
      - consultant_id
      - consultant_name
      - consultant_status
      - favorite_status
      - matching_result
      - rating
      - register_no
    SuitableConsultantRequestRequest:
      type: object
      description: Serializer for suitable consultant request parameters
      properties:
        consultant_type_filter_1:
          type: integer
          nullable: true
        consultant_type_filter_2:
          type: string
          nullable: true
        sort_by_matching:
          type: boolean
          default: false
        sort_by_latest:
          type: boolean
          default: false
    SuitableConsultantResponse:
      type: object
      description: Serializer for suitable consultant response
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/SuitableConsultantItem'
        project_info:
          type: object
          additionalProperties: {}
        page:
          type: integer
        per_page:
          type: integer
        total:
          type: integer
        has_next:
          type: boolean
      required:
      - has_next
      - page
      - per_page
      - project_info
      - results
      - total
    SurveysResponse:
      type: object
      description: Standard response serializer for surveys API
      properties:
        success:
          type: boolean
        error_code:
          type: string
          nullable: true
        error_message:
          type: string
          nullable: true
        data:
          type: object
          additionalProperties: {}
        api_version:
          type: string
      required:
      - api_version
      - data
      - error_code
      - error_message
      - success
    TcdAppFaq:
      type: object
      description: |-
        Serializer for FAQ model
        Includes category information and language-specific content
      properties:
        id:
          type: integer
          readOnly: true
        category:
          allOf:
          - $ref: '#/components/schemas/TcdAppFaqcategory'
          readOnly: true
        question_th:
          type: string
        question_en:
          type: string
        answer_th:
          type: string
        answer_en:
          type: string
        is_app:
          type: boolean
        is_web:
          type: boolean
        create_user:
          type: integer
          maximum: **********
          minimum: -**********
        create_date:
          type: string
          format: date-time
        update_date:
          type: string
          format: date-time
          nullable: true
        status:
          type: boolean
        app_faqcategory:
          type: integer
      required:
      - answer_en
      - answer_th
      - app_faqcategory
      - create_date
      - create_user
      - is_app
      - is_web
      - question_en
      - question_th
      - status
    TcdAppFaqcategory:
      type: object
      description: |-
        Serializer for FAQ Category model
        Provides both Thai and English names with display formatting
      properties:
        id:
          type: integer
          readOnly: true
        name_th:
          type: string
          maxLength: 255
        name_en:
          type: string
          maxLength: 255
        order:
          type: integer
          maximum: **********
          minimum: -**********
        display_text:
          type: string
          description: 'Create display text in format: name_th : name_en'
          readOnly: true
      required:
      - name_en
      - name_th
      - order
    TcdAppMasDepartment:
      type: object
      description: Serializer สำหรับข้อมูล Department
      properties:
        id:
          type: integer
          readOnly: true
        app_mas_ministry_id:
          type: integer
          readOnly: true
        name_th:
          type: string
          maxLength: 255
        name_en:
          type: string
          maxLength: 255
      required:
      - name_en
      - name_th
    TcdAppMasGovernmentSector:
      type: object
      description: Serializer สำหรับข้อมูล Government Sector
      properties:
        id:
          type: integer
          readOnly: true
        name_th:
          type: string
          maxLength: 255
        name_en:
          type: string
          maxLength: 255
      required:
      - name_en
      - name_th
    TcdAppMasMemberType:
      type: object
      description: Serializer สำหรับข้อมูล Member Type
      properties:
        id:
          type: integer
          readOnly: true
        name_th:
          type: string
          maxLength: 255
        name_en:
          type: string
          maxLength: 255
      required:
      - name_en
      - name_th
    TcdAppMasMinistry:
      type: object
      description: Serializer สำหรับข้อมูล Ministry
      properties:
        id:
          type: integer
          readOnly: true
        app_mas_government_sector_id:
          type: integer
          readOnly: true
        name_th:
          type: string
          maxLength: 255
        name_en:
          type: string
          maxLength: 255
      required:
      - name_en
      - name_th
    TcdAppNotification:
      type: object
      description: Serializer for TcdAppNotification model
      properties:
        id:
          type: integer
          readOnly: true
        noti_type:
          type: string
          maxLength: 50
        type:
          type: string
          nullable: true
          maxLength: 50
        main_id:
          type: integer
          maximum: **********
          minimum: -**********
          nullable: true
        ref_id:
          type: integer
          maximum: **********
          minimum: -**********
          nullable: true
        header:
          type: string
        detail:
          type: string
        create_date:
          type: string
          format: date-time
          readOnly: true
        is_read:
          type: boolean
      required:
      - detail
      - header
      - is_read
      - noti_type
    TcdAppNotificationRequest:
      type: object
      description: Serializer for TcdAppNotification model
      properties:
        noti_type:
          type: string
          minLength: 1
          maxLength: 50
        type:
          type: string
          nullable: true
          maxLength: 50
        main_id:
          type: integer
          maximum: **********
          minimum: -**********
          nullable: true
        ref_id:
          type: integer
          maximum: **********
          minimum: -**********
          nullable: true
        header:
          type: string
          minLength: 1
        detail:
          type: string
          minLength: 1
        is_read:
          type: boolean
      required:
      - detail
      - header
      - is_read
      - noti_type
    TcdCorporateType:
      type: object
      description: Serializer for TcdCorporateType model
      properties:
        id:
          type: integer
          readOnly: true
        name_th:
          type: string
          maxLength: 50
        name_en:
          type: string
          maxLength: 50
        type:
          type: integer
          maximum: **********
          minimum: -**********
      required:
      - name_en
      - name_th
      - type
    TcdDashboard:
      type: object
      description: Serializer for dashboard
      properties:
        id:
          type: integer
        dashboard_category_id:
          type: integer
        name_th:
          type: string
        name_en:
          type: string
        embed_url:
          type: string
        thumbnail:
          type: string
        is_consult:
          type: boolean
        is_matching:
          type: boolean
        is_application:
          type: boolean
        status:
          type: boolean
        create_user_id:
          type: integer
        create_date:
          type: string
          format: date-time
        update_user_id:
          type: integer
        update_date:
          type: string
          format: date-time
      required:
      - create_date
      - create_user_id
      - dashboard_category_id
      - embed_url
      - id
      - is_application
      - is_consult
      - is_matching
      - name_en
      - name_th
      - status
      - thumbnail
      - update_date
      - update_user_id
    TcdDashboardCategory:
      type: object
      description: Serializer for dashboard category
      properties:
        id:
          type: integer
        name_th:
          type: string
        name_en:
          type: string
        order:
          type: integer
        status:
          type: boolean
        create_date:
          type: string
          format: date-time
        update_date:
          type: string
          format: date-time
      required:
      - create_date
      - id
      - name_en
      - name_th
      - order
      - status
      - update_date
    TcdNews:
      type: object
      description: Serializer for TcdNews model
      properties:
        id:
          type: integer
          maximum: **********
          minimum: -**********
        newscategory:
          type: string
          readOnly: true
        name:
          type: string
          maxLength: 255
        detail:
          type: string
          nullable: true
        thumb:
          type: string
          nullable: true
        video:
          type: string
          nullable: true
          maxLength: 255
        newscategory_id:
          type: integer
          maximum: **********
          minimum: -**********
        count:
          type: integer
          maximum: **********
          minimum: -**********
        createdate:
          type: string
          format: date-time
        createuser:
          type: integer
          maximum: **********
          minimum: -**********
        status:
          type: string
          maxLength: 1
      required:
      - count
      - createdate
      - createuser
      - id
      - name
      - newscategory_id
      - status
    TcdSector:
      type: object
      description: Serializer สำหรับข้อมูล TcdSector (Sector Master Data)
      properties:
        id:
          type: integer
          readOnly: true
        code:
          type: string
          nullable: true
          maxLength: 255
        name_th:
          type: string
          nullable: true
          maxLength: 255
        name_en:
          type: string
          nullable: true
          maxLength: 255
        display_text:
          type: string
          description: 'สร้างข้อความแสดงผลในรูปแบบ: code : name_th : name_en'
          readOnly: true
    TcdService:
      type: object
      description: Serializer สำหรับข้อมูล TcdService (Service Master Data)
      properties:
        id:
          type: integer
          readOnly: true
        code:
          type: string
          nullable: true
          maxLength: 255
        name_th:
          type: string
          nullable: true
          maxLength: 255
        name_en:
          type: string
          nullable: true
          maxLength: 255
        display_text:
          type: string
          description: 'สร้างข้อความแสดงผลในรูปแบบ: code : name_th : name_en'
          readOnly: true
    TcdSkill:
      type: object
      description: Serializer สำหรับข้อมูล TcdSkill (Skill Master Data)
      properties:
        id:
          type: integer
          readOnly: true
        sector_id:
          type: integer
          maximum: **********
          minimum: -**********
          nullable: true
        code:
          type: string
          nullable: true
          maxLength: 255
        name_th:
          type: string
          nullable: true
          maxLength: 255
        name_en:
          type: string
          nullable: true
          maxLength: 255
        st:
          type: string
          nullable: true
          maxLength: 1
        nd:
          type: integer
          maximum: **********
          minimum: -**********
          nullable: true
        rd:
          type: string
          nullable: true
          maxLength: 1
        display_text:
          type: string
          description: 'สร้างข้อความแสดงผลในรูปแบบ: code : name_th : name_en'
          readOnly: true
        sector_info:
          type: string
          description: ดึงข้อมูลสาขาที่เกี่ยวข้อง
          readOnly: true
    TokenRequest:
      type: object
      properties:
        token:
          type: string
          minLength: 1
      required:
      - token
    UpdateMemberInfoRequest:
      type: object
      description: Serializer สำหรับการอัปเดตข้อมูลสมาชิก Member พร้อมการตรวจสอบรหัสผ่าน
      properties:
        current_password:
          type: string
          writeOnly: true
          minLength: 4
          description: รหัสผ่านปัจจุบันสำหรับยืนยันตัวตน
          maxLength: 50
        name:
          type: string
          nullable: true
          maxLength: 255
        first_name:
          type: string
          minLength: 1
          maxLength: 255
        last_name:
          type: string
          minLength: 1
          maxLength: 255
        email:
          type: string
          format: email
          minLength: 1
          maxLength: 50
        phone:
          type: string
          minLength: 1
          maxLength: 50
        identity_card_no:
          type: string
          nullable: true
          maxLength: 20
        website:
          type: string
          nullable: true
          maxLength: 255
        fb_id:
          type: string
          nullable: true
          maxLength: 50
        google_id:
          type: string
          nullable: true
          maxLength: 50
        apple_id:
          type: string
          nullable: true
          maxLength: 50
        app_mas_member_type_id:
          type: integer
          nullable: true
        app_mas_government_sector_id:
          type: integer
          nullable: true
        app_mas_ministry_id:
          type: integer
          nullable: true
        app_mas_department_id:
          type: integer
          nullable: true
        app_mas_government_sector_other:
          type: string
          nullable: true
          maxLength: 255
        app_mas_ministry_other:
          type: string
          nullable: true
          maxLength: 255
        app_mas_department_other:
          type: string
          nullable: true
          maxLength: 255
        is_notification:
          type: string
          minLength: 1
          maxLength: 1
        lang:
          type: string
          minLength: 1
          maxLength: 2
      required:
      - current_password
    UpdateTokenAppRequest:
      type: object
      properties:
        token_app:
          type: string
          minLength: 1
          description: token_app ใหม่
          maxLength: 250
      required:
      - token_app
    UploadProfilePictureRequest:
      type: object
      properties:
        profile_picture_base64:
          type: string
          minLength: 1
          description: รูปโปรไฟล์ในรูปแบบ base64 string (รองรับ JPG, JPEG, PNG, GIF
            ขนาดไม่เกิน 5MB)
      required:
      - profile_picture_base64
    ValidateRegisterRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          minLength: 1
          description: อีเมล
        phone:
          type: string
          minLength: 1
          description: เบอร์โทรศัพท์
        username:
          type: string
          minLength: 4
          description: ชื่อผู้ใช้
      required:
      - email
      - phone
      - username
    Worklist:
      type: object
      description: Serializer for TcdWorklist model with dynamic service type name
        and formatted date
      properties:
        id:
          type: integer
          readOnly: true
        service_type_name:
          type: string
          readOnly: true
        status_name:
          type: string
          readOnly: true
        status_work_name:
          type: string
          readOnly: true
        formatted_send_date:
          type: string
          readOnly: true
        request_type_id:
          type: integer
          maximum: **********
          minimum: -**********
        select_type_id:
          type: integer
          maximum: **********
          minimum: -**********
          nullable: true
        purpose_id:
          type: string
          format: decimal
          pattern: ^-?\d{0,18}(?:\.\d{0,0})?$
          nullable: true
        send_date:
          type: string
          format: date-time
        pdmo_number:
          type: string
          nullable: true
          maxLength: 50
        pdmo_date:
          type: string
          format: date-time
          nullable: true
        tcd_number:
          type: string
          nullable: true
          maxLength: 50
        tcd_date:
          type: string
          format: date-time
          nullable: true
        name:
          type: string
          nullable: true
        project_name:
          type: string
          nullable: true
        approve_date:
          type: string
          format: date-time
          nullable: true
        sign_date:
          type: string
          format: date-time
          nullable: true
        status:
          type: string
          nullable: true
          maxLength: 1
        status_work:
          type: string
          nullable: true
          maxLength: 1
        start_date:
          type: string
          format: date-time
          nullable: true
        last_date:
          type: string
          format: date-time
          nullable: true
        end_date:
          type: string
          format: date-time
          nullable: true
        user_consult_id:
          type: integer
          maximum: **********
          minimum: -**********
          nullable: true
        send_doc:
          type: string
          nullable: true
          maxLength: 1
        foreign:
          type: string
          nullable: true
          maxLength: 1
        register_no:
          type: string
          nullable: true
          maxLength: 50
        rating:
          type: string
          nullable: true
          maxLength: 50
        sector:
          type: string
          nullable: true
          maxLength: 250
        skill:
          type: string
          nullable: true
          maxLength: 250
        exp_consult:
          type: string
          nullable: true
          maxLength: 250
        user_approve_date:
          type: string
          format: date-time
          nullable: true
        ser_number:
          type: integer
          maximum: **********
          minimum: -**********
          nullable: true
        ser_year:
          type: string
          nullable: true
          maxLength: 4
        disapprove_date:
          type: string
          format: date-time
          nullable: true
        disapprove_remark:
          type: string
          nullable: true
      required:
      - request_type_id
      - send_date
    WorklistDetailResponse:
      type: object
      description: Serializer for worklist detail response data
      properties:
        id:
          type: integer
        request_type_id:
          type: integer
        select_type_id:
          type: integer
          nullable: true
        purpose_id:
          type: string
          format: decimal
          pattern: ^-?\d{0,18}(?:\.\d{0,0})?$
          nullable: true
        user_consult_id:
          type: integer
          nullable: true
        send_date:
          type: string
          format: date-time
          nullable: true
        pdmo_date:
          type: string
          format: date-time
          nullable: true
        tcd_date:
          type: string
          format: date-time
          nullable: true
        approve_date:
          type: string
          format: date-time
          nullable: true
        sign_date:
          type: string
          format: date-time
          nullable: true
        start_date:
          type: string
          format: date-time
          nullable: true
        last_date:
          type: string
          format: date-time
          nullable: true
        end_date:
          type: string
          format: date-time
          nullable: true
        user_approve_date:
          type: string
          format: date-time
          nullable: true
        disapprove_date:
          type: string
          format: date-time
          nullable: true
        pdmo_number:
          type: string
          nullable: true
          maxLength: 50
        tcd_number:
          type: string
          nullable: true
          maxLength: 50
        register_no:
          type: string
          nullable: true
          maxLength: 50
        ser_number:
          type: integer
          nullable: true
        ser_year:
          type: string
          nullable: true
          maxLength: 4
        name:
          type: string
          nullable: true
        project_name:
          type: string
          nullable: true
        status:
          type: string
          nullable: true
          maxLength: 1
        status_work:
          type: string
          nullable: true
          maxLength: 1
        status_work_name:
          type: string
          nullable: true
        latest_status:
          type: string
          nullable: true
        send_doc:
          type: string
          nullable: true
          maxLength: 1
        foreign:
          type: string
          nullable: true
          maxLength: 1
        rating:
          type: string
          nullable: true
          maxLength: 50
        sector:
          type: string
          nullable: true
          maxLength: 250
        skill:
          type: string
          nullable: true
          maxLength: 250
        exp_consult:
          type: string
          nullable: true
          maxLength: 250
        disapprove_remark:
          type: string
          nullable: true
        service_type_name:
          type: string
          nullable: true
        request_type_name:
          type: string
          nullable: true
        request_type_name_en:
          type: string
          nullable: true
        select_type_name_th:
          type: string
          nullable: true
        select_type_name_en:
          type: string
          nullable: true
      required:
      - approve_date
      - disapprove_date
      - disapprove_remark
      - end_date
      - exp_consult
      - foreign
      - id
      - last_date
      - latest_status
      - name
      - pdmo_date
      - pdmo_number
      - project_name
      - purpose_id
      - rating
      - register_no
      - request_type_id
      - request_type_name
      - request_type_name_en
      - sector
      - select_type_id
      - select_type_name_en
      - select_type_name_th
      - send_date
      - send_doc
      - ser_number
      - ser_year
      - service_type_name
      - sign_date
      - skill
      - start_date
      - status
      - status_work
      - status_work_name
      - tcd_date
      - tcd_number
      - user_approve_date
      - user_consult_id
    WorklistLogItem:
      type: object
      description: Serializer for individual worklist log item
      properties:
        id:
          type: integer
        worklist_id:
          type: integer
        status_name:
          type: string
        status_work_name:
          type: string
          nullable: true
        create_date:
          type: string
        expire_date:
          type: string
          nullable: true
        detail:
          type: string
          nullable: true
        document_url:
          type: string
          nullable: true
        edit_success_date:
          type: string
          format: date-time
          nullable: true
        status_work:
          type: string
        create_date_raw:
          type: string
          format: date-time
          nullable: true
        expire_date_raw:
          type: string
          format: date-time
          nullable: true
      required:
      - create_date
      - create_date_raw
      - detail
      - document_url
      - edit_success_date
      - expire_date
      - expire_date_raw
      - id
      - status_name
      - status_work
      - status_work_name
      - worklist_id
    WorklistLogResponse:
      type: object
      description: Serializer for worklist log response data
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/WorklistLogItem'
        pagination:
          type: object
          additionalProperties: {}
      required:
      - pagination
      - results
    WorklistResponse:
      type: object
      description: Serializer for worklist response data
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/Worklist'
        pagination:
          type: object
          additionalProperties: {}
      required:
      - pagination
      - results
  securitySchemes:
    CustomJWTAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
servers:
- url: http://localhost:8000
  description: Development server
- url: https://api.mcdc.com
  description: Production server
