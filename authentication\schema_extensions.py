"""
OpenAPI Schema Extensions for Authentication
"""
from drf_spectacular.extensions import OpenApiAuthenticationExtension
from drf_spectacular.plumbing import build_bearer_security_scheme_object


class CustomJWTAuthenticationExtension(OpenApiAuthenticationExtension):
    """
    OpenAPI extension for CustomJWTAuthentication
    """
    target_class = 'authentication.backends.CustomJWTAuthentication'
    name = 'CustomJWTAuth'
    priority = -1

    def get_security_definition(self, auto_schema):
        """
        Return the security definition for JWT authentication
        """
        return build_bearer_security_scheme_object(
            header_name='Authorization',
            token_prefix='Bearer',
            bearer_format='JWT'
        )
